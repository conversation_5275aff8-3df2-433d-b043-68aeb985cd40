'use client';
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import { Formik, Form, Field } from 'formik';
import { useState, useRef } from 'react';
import CommonTooltip from '@/Components/UI/CommonTooltip';
import { SolidInfoIcon } from '@/assets/svgIcons/SvgIcon';
import '@/css/account/Security.scss';

const UseRestoreCodeModal = ({ show, handleClose, onSubmit, isSubmitting, message }) => {
  const inputRefs = useRef([]);
  const [restoreCode, setRestoreCode] = useState('');

  const formatRestoreCode = (value) => {
    // Remove all non-alphanumeric characters and convert to uppercase
    const cleaned = value.replace(/[^A-Z0-9]/gi, '').toUpperCase();
    // Split into groups of 4 characters
    const groups = [];
    for (let i = 0; i < cleaned.length; i += 4) {
      groups.push(cleaned.slice(i, i + 4));
    }
    // Join with dashes, limit to 6 groups (24 characters total)
    return groups.slice(0, 6).join('-');
  };

  const handleInputChange = (e, setFieldValue) => {
    const value = e.target.value;
    const formatted = formatRestoreCode(value);
    setRestoreCode(formatted);
    setFieldValue('restore_code', formatted);
  };

  const handlePaste = (e, setFieldValue) => {
    e.preventDefault();
    const pasteData = e.clipboardData.getData('text');
    const formatted = formatRestoreCode(pasteData);
    setRestoreCode(formatted);
    setFieldValue('restore_code', formatted);
  };

  const handleSubmitForm = (values) => {
    onSubmit(values.restore_code);
  };

  const handleModalClose = () => {
    setRestoreCode('');
    handleClose();
  };

  return (
    <Modal
      show={show}
      onHide={handleModalClose}
      centered
      size="xl"
      contentClassName="custom-modal-content"
    >
      <div className="px-4 sm:px-6 py-6 sm:py-10 rounded-[15px] space-y-4 sm:space-y-6 max-w-full overflow-hidden">
        <h5 className="text-2xl sm:text-[32px] font-extrabold text-white text-left">
          Use Restore Code
        </h5>

        <p className="text-base sm:text-[20px] font-semibold text-white text-left">
          Paste your restoral code to proceed.
        </p>

        <Formik
          initialValues={{ restore_code: '' }}
          onSubmit={handleSubmitForm}
          enableReinitialize
        >
          {({ setFieldValue }) => (
            <Form>
              <div className="mb-4">
                <Field name="restore_code">
                  {({ field }) => (
                    <input
                      {...field}
                      type="text"
                      className="form-control text-white bg-white/20 border-white/30 rounded-md p-3 w-full text-lg tracking-wider restore-code-input"
                      placeholder="XXXX-XXXX-XXXX-XXXX-XXXX-XXXX"
                      value={restoreCode}
                      onChange={(e) => handleInputChange(e, setFieldValue)}
                      onPaste={(e) => handlePaste(e, setFieldValue)}
                      maxLength={29} // 24 characters + 5 dashes
                      style={{
                        letterSpacing: '0.1em',
                        fontFamily: 'monospace'
                      }}
                    />
                  )}
                </Field>
              </div>

              {message && (
                <div className={`text-start mt-3 mb-3 font-bold `}>
                  <p className={`${message.includes('successful') || message.includes('Redirecting') ? 'text-success' : 'text-[#ff696a]'}`}>{message}</p>
                </div>
              )}

              <div className="d-flex align-items-center gap-2 mb-4">
                {/* <CommonTooltip
                  content="Don't have a code? Generate one from your account settings next time you log in."
                  className="d-flex align-items-center"
                >
                  <SolidInfoIcon className="text-white/70" />
                </CommonTooltip> */}
                <span className="text-white text-sm">
                  Don't have a code? Generate one from your account settings next time you log in.
                </span>
              </div>

              <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mt-4">
                <Button
                  type="submit"
                  disabled={isSubmitting || !restoreCode || restoreCode.length < 29}
                  className="bg-[#00b7ff] text-white font-semibold !rounded-md w-full sm:w-[200px] disabled:opacity-50"
                >
                  {isSubmitting ? 'Verifying...' : 'Continue'}
                </Button>
                <Button
                  type="button"
                  onClick={handleModalClose}
                  className="!bg-[#B4B4B4] text-black font-semibold !rounded-md w-full sm:w-[200px]"
                >
                  Cancel
                </Button>
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </Modal>
  );
};

export default UseRestoreCodeModal;
