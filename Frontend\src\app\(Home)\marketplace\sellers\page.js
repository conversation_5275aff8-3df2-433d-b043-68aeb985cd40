"use client";

import { Col, Container, Row } from "react-bootstrap";
import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { debounce } from "lodash";
import CommonSearch from "@/Components/UI/CommonSearch";
import CustomPagination from "@/Components/UI/CustomPagination";
import HomeLayout from "@/Layouts/HomeLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import { ProfileUserDarkIcon, CrossIcon } from "@/assets/svgIcons/SvgIcon";
import "@/css/Home/Marketplace.scss";
import "@/css/Home/Category.scss";

const MarketplaceSellers = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  // State management
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedSellers, setSelectedSellers] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [filteredSellers, setFilteredSellers] = useState([]);
  const [isLoading, setIsLoading] = useState(false);

  // Static seller data (placeholder until Users API is available)
  const staticSellers = [
    {
      id: 1,
      username: "george_mans",
      firstName: "George",
      lastName: "Mans",
      isActive: true,
    },
    {
      id: 2,
      username: "john_doe",
      firstName: "John",
      lastName: "Doe",
      isActive: true,
    },
    {
      id: 3,
      username: "sarah_wilson",
      firstName: "Sarah",
      lastName: "Wilson",
      isActive: true,
    },
    {
      id: 4,
      username: "mike_johnson",
      firstName: "Mike",
      lastName: "Johnson",
      isActive: true,
    },
    {
      id: 5,
      username: "emma_davis",
      firstName: "Emma",
      lastName: "Davis",
      isActive: true,
    },
    {
      id: 6,
      username: "alex_brown",
      firstName: "Alex",
      lastName: "Brown",
      isActive: true,
    },
    {
      id: 7,
      username: "lisa_garcia",
      firstName: "Lisa",
      lastName: "Garcia",
      isActive: true,
    },
    {
      id: 8,
      username: "david_miller",
      firstName: "David",
      lastName: "Miller",
      isActive: true,
    },
    {
      id: 9,
      username: "jennifer_taylor",
      firstName: "Jennifer",
      lastName: "Taylor",
      isActive: true,
    },
    {
      id: 10,
      username: "robert_anderson",
      firstName: "Robert",
      lastName: "Anderson",
      isActive: true,
    },
    {
      id: 11,
      username: "maria_martinez",
      firstName: "Maria",
      lastName: "Martinez",
      isActive: true,
    },
    {
      id: 12,
      username: "james_wilson",
      firstName: "James",
      lastName: "Wilson",
      isActive: true,
    },
    {
      id: 13,
      username: "linda_moore",
      firstName: "Linda",
      lastName: "Moore",
      isActive: true,
    },
    {
      id: 14,
      username: "william_jackson",
      firstName: "William",
      lastName: "Jackson",
      isActive: true,
    },
    {
      id: 15,
      username: "patricia_white",
      firstName: "Patricia",
      lastName: "White",
      isActive: true,
    },
    {
      id: 16,
      username: "richard_harris",
      firstName: "Richard",
      lastName: "Harris",
      isActive: true,
    },
    {
      id: 17,
      username: "barbara_clark",
      firstName: "Barbara",
      lastName: "Clark",
      isActive: true,
    },
    {
      id: 18,
      username: "charles_lewis",
      firstName: "Charles",
      lastName: "Lewis",
      isActive: true,
    },
    {
      id: 19,
      username: "susan_walker",
      firstName: "Susan",
      lastName: "Walker",
      isActive: true,
    },
    {
      id: 20,
      username: "joseph_hall",
      firstName: "Joseph",
      lastName: "Hall",
      isActive: true,
    },
    {
      id: 21,
      username: "nancy_allen",
      firstName: "Nancy",
      lastName: "Allen",
      isActive: true,
    },
    {
      id: 22,
      username: "thomas_young",
      firstName: "Thomas",
      lastName: "Young",
      isActive: true,
    },
    {
      id: 23,
      username: "betty_king",
      firstName: "Betty",
      lastName: "King",
      isActive: true,
    },
    {
      id: 24,
      username: "christopher_wright",
      firstName: "Christopher",
      lastName: "Wright",
      isActive: true,
    },
    {
      id: 25,
      username: "helen_lopez",
      firstName: "Helen",
      lastName: "Lopez",
      isActive: true,
    },
    {
      id: 26,
      username: "daniel_hill",
      firstName: "Daniel",
      lastName: "Hill",
      isActive: true,
    },
    {
      id: 27,
      username: "sandra_scott",
      firstName: "Sandra",
      lastName: "Scott",
      isActive: true,
    },
    {
      id: 28,
      username: "matthew_green",
      firstName: "Matthew",
      lastName: "Green",
      isActive: true,
    },
    {
      id: 29,
      username: "donna_adams",
      firstName: "Donna",
      lastName: "Adams",
      isActive: true,
    },
    {
      id: 30,
      username: "anthony_baker",
      firstName: "Anthony",
      lastName: "Baker",
      isActive: true,
    },
    {
      id: 31,
      username: "carol_gonzalez",
      firstName: "Carol",
      lastName: "Gonzalez",
      isActive: true,
    },
    {
      id: 32,
      username: "mark_nelson",
      firstName: "Mark",
      lastName: "Nelson",
      isActive: true,
    },
    {
      id: 33,
      username: "ruth_carter",
      firstName: "Ruth",
      lastName: "Carter",
      isActive: true,
    },
    {
      id: 34,
      username: "donald_mitchell",
      firstName: "Donald",
      lastName: "Mitchell",
      isActive: true,
    },
    {
      id: 35,
      username: "sharon_perez",
      firstName: "Sharon",
      lastName: "Perez",
      isActive: true,
    },
    {
      id: 36,
      username: "steven_roberts",
      firstName: "Steven",
      lastName: "Roberts",
      isActive: true,
    },
    {
      id: 37,
      username: "michelle_turner",
      firstName: "Michelle",
      lastName: "Turner",
      isActive: true,
    },
    {
      id: 38,
      username: "paul_phillips",
      firstName: "Paul",
      lastName: "Phillips",
      isActive: true,
    },
    {
      id: 39,
      username: "emily_campbell",
      firstName: "Emily",
      lastName: "Campbell",
      isActive: true,
    },
    {
      id: 40,
      username: "andrew_parker",
      firstName: "Andrew",
      lastName: "Parker",
      isActive: true,
    },
    {
      id: 41,
      username: "kimberly_evans",
      firstName: "Kimberly",
      lastName: "Evans",
      isActive: true,
    },
    {
      id: 42,
      username: "joshua_edwards",
      firstName: "Joshua",
      lastName: "Edwards",
      isActive: true,
    },
    {
      id: 43,
      username: "lisa_collins",
      firstName: "Lisa",
      lastName: "Collins",
      isActive: true,
    },
    {
      id: 44,
      username: "kenneth_stewart",
      firstName: "Kenneth",
      lastName: "Stewart",
      isActive: true,
    },
    {
      id: 45,
      username: "amanda_sanchez",
      firstName: "Amanda",
      lastName: "Sanchez",
      isActive: true,
    },
    {
      id: 46,
      username: "kevin_morris",
      firstName: "Kevin",
      lastName: "Morris",
      isActive: true,
    },
    {
      id: 47,
      username: "deborah_rogers",
      firstName: "Deborah",
      lastName: "Rogers",
      isActive: true,
    },
    {
      id: 48,
      username: "brian_reed",
      firstName: "Brian",
      lastName: "Reed",
      isActive: true,
    },
    {
      id: 49,
      username: "dorothy_cook",
      firstName: "Dorothy",
      lastName: "Cook",
      isActive: true,
    },
    {
      id: 50,
      username: "gary_morgan",
      firstName: "Gary",
      lastName: "Morgan",
      isActive: true,
    },
  ];

  const SELLERS_PER_PAGE = 20;
  const MAX_SELECTED_SELLERS = 10;

  // Meta data
  const metaArray = {
    title: "TradeReply Marketplace Sellers",
    description:
      "Looking for specific sellers? Search by name or username, add them to your filter list, then click 'Go to Marketplace' to browse their products only.",
    canonical_link: "https://www.tradereply.com/marketplace/sellers",
    og_site_name: "TradeReply",
    og_title: "TradeReply Marketplace Sellers",
    og_description:
      "Looking for specific sellers? Search by name or username, add them to your filter list, then click 'Go to Marketplace' to browse their products only.",
    og_url: "https://www.tradereply.com/marketplace/sellers",
    og_type: "website",
    og_image:
      "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
    og_image_width: "1200",
    og_image_height: "630",
    og_locale: "en_US",
    twitter_card: "summary_large_image",
    twitter_title: "TradeReply Marketplace Sellers",
    twitter_description:
      "Looking for specific sellers? Search by name or username, add them to your filter list, then click 'Go to Marketplace' to browse their products only.",
    twitter_image:
      "https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png",
    twitter_site: "@JoinTradeReply",
    robots: "noindex, follow",
  };

  // Initialize from URL params
  useEffect(() => {
    const query = searchParams.get("search") || "";
    const page = parseInt(searchParams.get("page")) || 1;
    const sellers = searchParams.get("sellers");

    setSearchQuery(query);
    setCurrentPage(page);

    if (sellers) {
      try {
        const sellerList = JSON.parse(decodeURIComponent(sellers));
        setSelectedSellers(sellerList);
      } catch (error) {
        console.error("Error parsing sellers from URL:", error);
      }
    }
  }, [searchParams]);

  // Fuzzy search function
  const fuzzySearch = (sellers, query) => {
    if (!query.trim()) return sellers;

    const lowerQuery = query.toLowerCase();

    return sellers
      .filter((seller) => {
        const firstName = seller.firstName?.toLowerCase() || "";
        const lastName = seller.lastName?.toLowerCase() || "";
        const username = seller.username?.toLowerCase() || "";
        const fullName = `${firstName} ${lastName}`.toLowerCase();

        return (
          firstName.includes(lowerQuery) ||
          lastName.includes(lowerQuery) ||
          username.includes(lowerQuery) ||
          fullName.includes(lowerQuery)
        );
      })
      .sort((a, b) => {
        // Prioritize matches that start with the query
        const aFirstName = a.firstName?.toLowerCase() || "";
        const aLastName = a.lastName?.toLowerCase() || "";
        const aUsername = a.username?.toLowerCase() || "";
        const aFullName = `${aFirstName} ${aLastName}`;

        const bFirstName = b.firstName?.toLowerCase() || "";
        const bLastName = b.lastName?.toLowerCase() || "";
        const bUsername = b.username?.toLowerCase() || "";
        const bFullName = `${bFirstName} ${bLastName}`;

        const aStartsWithFirst = aFirstName.startsWith(lowerQuery);
        const aStartsWithLast = aLastName.startsWith(lowerQuery);
        const aStartsWithUsername = aUsername.startsWith(lowerQuery);
        const aStartsWithFull = aFullName.startsWith(lowerQuery);

        const bStartsWithFirst = bFirstName.startsWith(lowerQuery);
        const bStartsWithLast = bLastName.startsWith(lowerQuery);
        const bStartsWithUsername = bUsername.startsWith(lowerQuery);
        const bStartsWithFull = bFullName.startsWith(lowerQuery);

        const aStartsWith =
          aStartsWithFirst ||
          aStartsWithLast ||
          aStartsWithUsername ||
          aStartsWithFull;
        const bStartsWith =
          bStartsWithFirst ||
          bStartsWithLast ||
          bStartsWithUsername ||
          bStartsWithFull;

        if (aStartsWith && !bStartsWith) return -1;
        if (!aStartsWith && bStartsWith) return 1;

        return 0;
      });
  };

  // Filter sellers based on search query
  useEffect(() => {
    const filtered = fuzzySearch(staticSellers, searchQuery);
    setFilteredSellers(filtered);
    setCurrentPage(1); // Reset to first page when search changes
  }, [searchQuery]);

  // Debounced search handler
  const debouncedSearch = useCallback(
    debounce((searchTerm) => {
      setSearchQuery(searchTerm);
      updateURL({ search: searchTerm, page: 1 });
    }, 300),
    []
  );

  const handleSearchChange = (event) => {
    debouncedSearch(event.target.value);
  };

  // Update URL with current state
  const updateURL = (updates) => {
    const params = new URLSearchParams(searchParams);

    Object.entries(updates).forEach(([key, value]) => {
      if (value && value !== "" && value !== 1) {
        params.set(key, value);
      } else {
        params.delete(key);
      }
    });

    if (selectedSellers.length > 0) {
      params.set(
        "sellers",
        encodeURIComponent(JSON.stringify(selectedSellers))
      );
    } else {
      params.delete("sellers");
    }

    const newURL = `/marketplace/sellers${
      params.toString() ? `?${params.toString()}` : ""
    }`;
    router.push(newURL, { scroll: false });
  };

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
    updateURL({ search: searchQuery, page });
  };

  // Add seller to selection
  const handleAddSeller = (seller) => {
    if (selectedSellers.length >= MAX_SELECTED_SELLERS) {
      return; // Button should be disabled, but just in case
    }

    if (!selectedSellers.find((s) => s.id === seller.id)) {
      const newSelectedSellers = [...selectedSellers, seller];
      setSelectedSellers(newSelectedSellers);
      updateURL({ search: searchQuery, page: currentPage });
    }
  };

  // Remove seller from selection
  const handleRemoveSeller = (sellerId) => {
    const newSelectedSellers = selectedSellers.filter((s) => s.id !== sellerId);
    setSelectedSellers(newSelectedSellers);
    updateURL({ search: searchQuery, page: currentPage });
  };

  // Clear search
  const handleClearSearch = () => {
    setSearchQuery("");
    updateURL({ page: currentPage });
  };

  // Navigate to marketplace with selected sellers
  const handleGoToMarketplace = () => {
    const params = new URLSearchParams();
    if (selectedSellers.length > 0) {
      params.set(
        "sellers",
        encodeURIComponent(JSON.stringify(selectedSellers))
      );
    }
    router.push(
      `/marketplace${params.toString() ? `?${params.toString()}` : ""}`
    );
  };

  // Calculate pagination
  const totalSellers = filteredSellers.length;
  const totalPages = Math.ceil(totalSellers / SELLERS_PER_PAGE);
  const startIndex = (currentPage - 1) * SELLERS_PER_PAGE;
  const endIndex = startIndex + SELLERS_PER_PAGE;
  const currentSellers = filteredSellers.slice(startIndex, endIndex);

  // Create pagination links object for CustomPagination component
  const paginationLinks = {
    total: totalPages,
    current_page: currentPage,
    prev_page: currentPage > 1 ? currentPage - 1 : null,
    next_page: currentPage < totalPages ? currentPage + 1 : null,
  };

  return (
    <HomeLayout>
      <MetaHead props={metaArray} />
      <div className="marketplace py-100">
        <Container>
          <section className="marketplace_inner">
            <div className="marketplace_heading text-center">
              <h1>TradeReply Marketplace Sellers</h1>
              <p
                className="mt-3 mb-4"
                style={{ fontSize: "1.125rem", color: "#c5c5d5" }}
              >
                Looking for specific sellers? Search by name or username, add
                them to your filter list, then click "Go to Marketplace" to
                browse their products only.
              </p>
            </div>

            {/* Search Section */}
            <div className="categorySec_search mb-4">
              <CommonSearch
                placeholder="Search Sellers by Name or Username"
                icon={true}
                name="sellerSearch"
                onChange={handleSearchChange}
                value={searchQuery}
              />
            </div>

            {/* Go to Marketplace Button */}
            <div className="mb-4">
              <button
                type="button"
                className="btn"
                style={{
                  backgroundColor: "#00ADEF",
                  color: "#fff",
                  borderRadius: "25px",
                  padding: "10px 30px",
                  fontSize: "16px",
                  fontWeight: "600",
                  border: "none",
                }}
                onClick={handleGoToMarketplace}
              >
                Go to<br></br> Marketplace
              </button>
            </div>

            {/* Horizontal Separator */}
            <div
              className="mb-4"
              style={{
                height: "3px",
                backgroundColor: "rgba(0, 173, 239, 0.3)",
                borderRadius: "2px",
              }}
            ></div>

            {/* Filter Tags and Pagination Row */}
            <div className="d-flex justify-content-between align-items-center mb-4">
              {/* Filter Tags */}
              <div className="d-flex flex-wrap gap-3 align-items-center">
                {/* Search Tag */}
                {searchQuery && (
                  <div className="categorySec_fliters_boxadd">
                    <h6 className="d-flex align-items-center gap-2 mb-0">
                      <span>Search: {searchQuery}</span>
                      <span
                        className="cursor-pointer"
                        onClick={handleClearSearch}
                        style={{ cursor: "pointer" }}
                      >
                        <CrossIcon />
                      </span>
                    </h6>
                  </div>
                )}

                {/* Selected Seller Tags */}
                {selectedSellers.map((seller) => (
                  <div key={seller.id} className="categorySec_fliters_boxadd">
                    <h6 className="d-flex align-items-center gap-2 mb-0">
                      <span>Seller={seller.username}</span>
                      <span
                        className="cursor-pointer"
                        onClick={() => handleRemoveSeller(seller.id)}
                        style={{ cursor: "pointer" }}
                      >
                        <CrossIcon />
                      </span>
                    </h6>
                  </div>
                ))}
              </div>

              {/* Pagination */}
              <div>
                {totalPages > 1 && (
                  <CustomPagination
                    links={paginationLinks}
                    onDataSend={handlePageChange}
                    useLinks={false}
                    pageUrl="marketplace/sellers"
                  />
                )}
              </div>
            </div>

            {/* Results Table */}
            <Row>
              <Col xs={12}>
                {/* Table Container with White Background */}
                <div
                  style={{
                    backgroundColor: "#fff",
                    borderRadius: "15px",
                    overflow: "hidden",
                  }}
                >
                  {/* Dark Blue Header Strip */}
                  <div className="p-3">
                    <div
                      className="d-flex justify-content-between align-items-center px-4 py-3"
                      style={{
                        backgroundColor: "#042053",
                        borderRadius: "15px 15px 0 0",
                      }}
                    >
                      <h6
                        className="mb-0"
                        style={{ color: "#fff", fontWeight: "600" }}
                      >
                        Marketplace Sellers ({totalSellers})
                      </h6>
                      <h6
                        className="mb-0"
                        style={{ color: "#fff", fontWeight: "600" }}
                      >
                        {selectedSellers.length} of {MAX_SELECTED_SELLERS} Added
                      </h6>
                    </div>

                    {/* Sellers List */}
                    <div style={{ padding: "0" }}>
                      {currentSellers.length > 0 ? (
                        currentSellers.map((seller, index) => {
                          const isSelected = selectedSellers.find(
                            (s) => s.id === seller.id
                          );
                          return (
                            <div
                              key={seller.id}
                              className="d-flex align-items-center px-4 py-3"
                              style={{
                                borderBottom:
                                  index < currentSellers.length - 1
                                    ? "1px solid #e9ecef"
                                    : "none",
                                backgroundColor: "#fff",
                              }}
                            >
                              {/* Add/Added Button */}
                              <div className="me-3">
                                {isSelected ? (
                                  <button
                                    type="button"
                                    className="btn btn-sm"
                                    style={{
                                      backgroundColor: "#6c757d",
                                      color: "#fff",
                                      borderRadius: "20px",
                                      padding: "6px 16px",
                                      fontSize: "12px",
                                      fontWeight: "600",
                                      border: "none",
                                      minWidth: "70px",
                                    }}
                                    disabled
                                  >
                                    Added
                                  </button>
                                ) : (
                                  <button
                                    type="button"
                                    className="btn btn-sm"
                                    style={{
                                      backgroundColor:
                                        selectedSellers.length >=
                                        MAX_SELECTED_SELLERS
                                          ? "#6c757d"
                                          : "#00ADEF",
                                      color: "#fff",
                                      borderRadius: "20px",
                                      padding: "6px 16px",
                                      fontSize: "12px",
                                      fontWeight: "600",
                                      border: "none",
                                      cursor:
                                        selectedSellers.length >=
                                        MAX_SELECTED_SELLERS
                                          ? "not-allowed"
                                          : "pointer",
                                      minWidth: "70px",
                                    }}
                                    onClick={() => handleAddSeller(seller)}
                                    disabled={
                                      selectedSellers.length >=
                                      MAX_SELECTED_SELLERS
                                    }
                                  >
                                    Add
                                  </button>
                                )}
                              </div>

                              {/* Profile Icon */}
                              <div className="me-3">
                                <ProfileUserDarkIcon />
                              </div>

                              {/* Seller Name */}
                              <div style={{ flex: 1 }}>
                                <h6
                                  style={{
                                    color: "#042053",
                                    marginBottom: "2px",
                                    fontWeight: "600",
                                  }}
                                >
                                  {seller.firstName} {seller.lastName}
                                </h6>
                                <p
                                  style={{
                                    color: "#6c757d",
                                    fontSize: "14px",
                                    margin: 0,
                                  }}
                                >
                                  @{seller.username}
                                </p>
                              </div>
                            </div>
                          );
                        })
                      ) : (
                        <div className="text-center py-5">
                          <p style={{ color: "#6c757d", fontSize: "18px" }}>
                            {searchQuery
                              ? `No sellers found matching "${searchQuery}"`
                              : "No sellers available"}
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Bottom Pagination */}
                {totalPages > 1 && (
                  <div className="d-flex justify-content-end mt-4">
                    <CustomPagination
                      links={paginationLinks}
                      onDataSend={handlePageChange}
                      useLinks={false}
                      pageUrl="marketplace/sellers"
                    />
                  </div>
                )}
              </Col>
            </Row>
          </section>
        </Container>
      </div>
    </HomeLayout>
  );
};

export default MarketplaceSellers;
