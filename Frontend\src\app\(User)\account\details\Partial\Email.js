'use client';
import React, { useState, useEffect } from 'react';
import { Col } from 'react-bootstrap';
import { EditIconSvg } from '@/assets/svgIcons/SvgIcon';
import Link from 'next/link';
import { maskEmail } from '@/utils/emailMask';
import { useSelector, useDispatch } from 'react-redux';
import { setUser } from '@/redux/authSlice';
import { get } from '@/utils/apiUtils';
import { usePathname } from 'next/navigation';


export default function Email({ userData }) {
    const dispatch = useDispatch();
    const reduxUser = useSelector((state) => state?.auth?.user || null);
    const pathname = usePathname();


    // Return null if no userData is provided
    if (!userData) {
        return null;
    }

    const getDisplayEmail = () => {
        if (!userData?.email) return 'Not set';
        return maskEmail(userData.email);
    };

    return (
        <>
            <Col lg={12} xs={12} className="mb-3 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Email</h6>
                        </div>
                        <div className="common_blackcard_innerheader_icon">
                            <Link href={`/account/email/setup?from=${encodeURIComponent(pathname)}`} prefetch={true}>
                                <button className="d-flex align-items-center">
                                    <EditIconSvg />
                                    <span className="ms-2">Update</span>
                                </button>
                            </Link>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list">
                            <ul>
                                <li>
                                    <Col xs={12} md={3}>
                                        <span>Email </span>
                                    </Col>
                                    <Col xs={12} md={3}>
                                        <span>{getDisplayEmail()}</span>
                                    </Col>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </Col >
        </>
    )
}
