/* [project]/src/css/account/AccountLayout.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.Account_layout_main {
  display: flex;
}

.Account_layout_rightaside {
  color: #fff;
  width: calc(100% - 355px);
  padding: 2.813rem 1.875rem;
  position: relative;
}

@media (min-width: 992px) {
  .Account_layout_rightaside .mb-lg-4 {
    margin-bottom: 1.875rem !important;
  }
}

@media (max-width: 1199px) {
  .Account_layout_rightaside {
    width: 100%;
    padding: 2.813rem 1rem;
  }
}

.sidebar_heading {
  width: 100%;
  margin-bottom: 26px;
}

@media screen and (max-width: 1199px) {
  .sidebar_heading {
    padding-left: 45px;
  }
}

.sidebar_heading_top {
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  display: flex;
}

.sidebar_heading h2 {
  font-family: <PERSON><PERSON>-<PERSON>, sans-serif;
  font-size: 48px;
  font-weight: 400;
  display: inline-flex;
}

@media (max-width: 1199px) {
  .sidebar_heading h2 {
    font-size: 1.688rem;
  }
}

@media (max-width: 767px) {
  .sidebar_heading h2 {
    font-size: 1.5rem;
  }
}

.sidebar_heading_icon button {
  color: #00adef;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  padding: 0;
  font-size: 1.15rem;
  font-weight: 600;
  transition: all .3s ease-in-out;
}

@media (max-width: 991px) {
  .sidebar_heading_icon button {
    justify-content: flex-end;
    align-items: center;
    font-size: 14px;
    display: flex;
  }
}

.sidebar_heading_icon button .me-2 {
  margin-right: 10px !important;
}

.sidebar_heading_icon button .ms-2 {
  margin-left: 10px !important;
}

.sidebar_heading_icon button svg {
  width: 16px;
  margin-left: 10px;
  transition: all .3s ease-in-out;
}

@media (max-width: 991px) {
  .sidebar_heading_icon button svg {
    width: 14px;
  }
}

@media (max-width: 767px) {
  .sidebar_heading_icon button svg {
    width: 10px;
    margin-left: 0;
  }
}

.sidebar_heading_icon button svg path {
  fill: #00adef;
}

.sidebar_heading_icon button:hover {
  color: #fea500;
}

.sidebar_heading_icon button:hover svg {
  margin-left: 12px;
}

@media (max-width: 991px) {
  .sidebar_heading_icon button:hover svg {
    margin-left: 10px;
  }
}

.sidebar_heading_icon button:hover svg path {
  fill: #fea500;
}

.common_blackcard, .common_whitecard {
  background-color: #191c23;
  border: 1px solid #04498c;
  border-radius: 15px;
  width: 100%;
  padding: 10px;
}

.common_blackcard_innerheader, .common_whitecard_innerheader {
  background-color: #031940;
  border-radius: 15px;
  justify-content: space-between;
  align-items: center;
  padding: .625rem 1.25rem;
  font-family: Gilroy-Semibold, sans-serif;
  font-weight: 400;
  display: flex;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader, .common_whitecard_innerheader {
    padding: .625rem;
  }
}

.common_blackcard_innerheader p, .common_whitecard_innerheader p {
  color: #fff;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader p, .common_whitecard_innerheader p {
    font-size: 14px;
  }
}

@media (max-width: 767px) {
  .common_blackcard_innerheader p, .common_whitecard_innerheader p {
    font-size: 14px;
  }
}

.common_blackcard_innerheader_content, .common_whitecard_innerheader_content {
  padding-right: 30px;
  font-size: 18px;
  font-weight: 400;
}

.common_blackcard_innerheader_content h6, .common_whitecard_innerheader_content h6 {
  font-weight: 600;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_content, .common_whitecard_innerheader_content {
    padding-right: 15px;
  }

  .common_blackcard_innerheader_content h6, .common_whitecard_innerheader_content h6 {
    font-size: 18px !important;
  }
}

@media (max-width: 767px) {
  .common_blackcard_innerheader_content, .common_whitecard_innerheader_content {
    padding-right: 5px;
  }
}

@media (max-width: 400px) {
  .common_blackcard_innerheader_content h6, .common_whitecard_innerheader_content h6 {
    font-size: 14px !important;
  }
}

.common_blackcard_innerheader_icon button, .common_whitecard_innerheader_icon button {
  color: #00adef;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  padding: 0;
  font-family: Gilroy-Semibold, sans-serif;
  font-size: 18px;
  font-weight: 600;
  transition: all .3s ease-in-out;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_icon button, .common_whitecard_innerheader_icon button {
    justify-content: flex-end;
    align-items: center;
    font-size: 14px;
    display: flex;
  }
}

.common_blackcard_innerheader_icon button .me-2, .common_whitecard_innerheader_icon button .me-2 {
  margin-right: 10px !important;
}

.common_blackcard_innerheader_icon button .ms-2, .common_whitecard_innerheader_icon button .ms-2 {
  margin-left: 10px !important;
}

.common_blackcard_innerheader_icon button .link-icon, .common_whitecard_innerheader_icon button .link-icon {
  flex-shrink: 0;
  height: auto;
}

.common_blackcard_innerheader_icon button svg, .common_whitecard_innerheader_icon button svg {
  width: 16px;
  transition: all .3s ease-in-out;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_icon button svg, .common_whitecard_innerheader_icon button svg {
    width: 14px;
  }
}

@media (max-width: 767px) {
  .common_blackcard_innerheader_icon button svg, .common_whitecard_innerheader_icon button svg {
    width: 10px;
    margin-left: 0;
  }
}

.common_blackcard_innerheader_icon button svg path, .common_whitecard_innerheader_icon button svg path {
  fill: #00adef;
}

.common_blackcard_innerheader_icon button:hover, .common_whitecard_innerheader_icon button:hover {
  color: #fea500;
}

.common_blackcard_innerheader_icon button:hover svg, .common_whitecard_innerheader_icon button:hover svg {
  margin-left: 12px;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_icon button:hover svg, .common_whitecard_innerheader_icon button:hover svg {
    margin-left: 10px;
  }
}

.common_blackcard_innerheader_icon button:hover svg path, .common_whitecard_innerheader_icon button:hover svg path {
  fill: #fea500;
}

.common_blackcard_innerheader_tradeacct, .common_whitecard_innerheader_tradeacct {
  width: 200px;
  min-width: 200px;
}

.common_blackcard_innerheader_tradeacct h6, .common_whitecard_innerheader_tradeacct h6 {
  color: #fff;
  background-color: #04498c;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  padding: 8px 15px;
  font-size: 1.25rem;
  line-height: normal;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_tradeacct h6, .common_whitecard_innerheader_tradeacct h6 {
    font-size: 16px;
  }
}

.common_blackcard_innerheader_tradeacct p, .common_whitecard_innerheader_tradeacct p {
  color: #000;
  background-color: #fff;
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
  margin: 0;
  padding: 8px 15px;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: normal;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_tradeacct p, .common_whitecard_innerheader_tradeacct p {
    font-size: 16px;
  }
}

.common_blackcard_innerbody, .common_whitecard_innerbody {
  padding: 1.25rem .625rem;
}

.connetionTable .common_blackcard_innerbody, .connetionTable .common_whitecard_innerbody, .removePadding .common_blackcard_innerbody, .removePadding .common_whitecard_innerbody {
  padding: 0;
}

@media (max-width: 991px) {
  .account_card.pullcontent .common_blackcard_innerheader_icon, .account_card.pullcontent .common_whitecard_innerheader_icon {
    margin-top: 10px;
  }
}

.common_whitecard {
  background-color: #fff !important;
}

.common_whitecard_innerbody {
  padding: .8rem .3rem 0;
}

.account_card .label {
  color: #fff;
  font-size: 1.125rem;
  font-weight: 600;
}

.account_card_list_btns {
  justify-content: end;
  align-items: center;
  gap: 10px;
  display: flex;
}

.account_card_list_btns .btn-style {
  min-height: 40px;
  font-size: 16px !important;
}

.account_card_list_form {
  width: 40%;
}

.account_card_list_form .row {
  width: 100% !important;
}

.account_card_list ul li {
  color: #fff;
  justify-content: space-between;
  margin-bottom: 1.25rem;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
}

@media (max-width: 991px) {
  .account_card_list ul li {
    margin-bottom: 1rem;
    font-size: 15px;
  }
}

.account_card_list ul li:last-child {
  margin-bottom: 0;
}

.account_card_list ul li span {
  color: #fff;
}

.account_card_list p {
  color: #c5c5c5;
}

.account_card_redeem .form-control {
  width: calc(100% - 136px);
  margin-right: 10px;
}

.account_card_redeem ::placeholder {
  color: #fff;
  opacity: 1;
}

.account_card_redeem .btn-style {
  min-width: 150px;
  min-height: 56px;
  padding: 8px 15px;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
}

@media (max-width: 991px) {
  .account_card_redeem .btn-style {
    min-width: 130px;
    min-height: 52px;
    padding: 8px 15px;
    font-size: 14px;
    line-height: 24px;
  }
}

.account_card_redeem .error-messages .success {
  color: #32cd33;
}

.account_card_redeem .error-messages .invalid {
  color: #ff696a;
}

.account_card_checkup_verify {
  width: calc(100% - 150px);
  padding-right: 30px;
}

@media (max-width: 991px) {
  .account_card_checkup_verify {
    width: calc(100% - 100px);
    padding-right: 0;
  }
}

.account_card_checkup_chart {
  text-align: center;
  width: 150px;
}

@media (max-width: 991px) {
  .account_card_checkup_chart {
    width: 100px;
  }

  .account_card_checkup_chart .CircularProgressbar_text h6 {
    font-size: 14px;
  }
}

.account_card_subscription_list {
  flex-wrap: wrap;
  display: flex;
}

.account_card_subscription_list li {
  width: 25%;
}

@media (max-width: 767px) {
  .account_card_subscription_list li {
    width: 50%;
    padding: 5px 0;
  }

  .account_card_subscription_list li:nth-child(2n) {
    text-align: right;
  }
}

.account_card_subscription_list li p {
  color: #fff;
  margin-top: .5rem;
  font-weight: 600;
}

@media (max-width: 991px) {
  .account_card_subscription_list li p {
    font-size: 14px;
    line-height: 20px;
  }
}

@media (max-width: 767px) {
  .account_card_subscription_list li p {
    margin-top: 0;
  }
}

@media (max-width: 991px) {
  .account_card_subscription_list li h6 {
    font-size: 15px;
  }
}

.account_card_table .simple_table_imgIcon {
  min-width: 20px;
}

.account_card_table .tableless {
  border: 0;
}

.account_card_table .tableless:before {
  display: none;
}

.account_card_table .tableless .common_table tr th, .account_card_table .tableless .common_table tr td {
  border: 0;
}

.account_card_table .tableless .common_table tr td {
  color: #fff;
  border-bottom: 1px solid #666;
  font-weight: 600 !important;
}

.account_card_table .tableless .common_table thead tr {
  border-radius: 15px;
}

.account_card_table .tableless .common_table thead tr th {
  background-color: #031940;
  padding: 10px 15px;
  font-size: 1rem;
}

.account_card_table .tableless .common_table thead tr th:first-child {
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
}

.account_card_table .tableless .common_table thead tr th:last-child {
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}

.account_card .add_phone_number, .account_card .add_number, .account_card .blue_text_btn {
  color: #00adef;
  letter-spacing: -.1px;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  padding: 0;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 24.5px;
  transition: all .3s ease-in-out;
}

@media (max-width: 991px) {
  .account_card .add_phone_number, .account_card .add_number, .account_card .blue_text_btn {
    font-size: 1rem;
  }
}

.account_card .add_phone_number:hover, .account_card .add_number:hover, .account_card .blue_text_btn:hover {
  color: #fea500;
}

.account_card .add_number svg, .account_card .add_number img, .account_card .blue_text_btn svg, .account_card .blue_text_btn img {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}

.account_card .add_number svg path, .account_card .add_number img path, .account_card .blue_text_btn svg path, .account_card .blue_text_btn img path {
  fill: #00adef;
}

.account_card .add_number:hover svg path, .account_card .blue_text_btn:hover svg path {
  fill: #fea500;
}

.CircularProgressbar_text h6 {
  font-size: 14px !important;
}

.account-custom-select {
  position: relative;
}

.account-custom-select .header {
  min-height: 56px;
  box-shadow: none;
  color: #fff;
  cursor: pointer;
  background-color: rgba(255, 255, 255, .3);
  border-radius: 1rem;
  outline: none;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: .5rem 1.25rem;
  font-size: 1rem;
  display: flex;
}

.account-custom-select .header span {
  width: 100% !important;
}

.account-custom-select .body {
  z-index: 999;
  background-color: #191c23;
  border: 1px solid rgba(255, 255, 255, .3);
  border-radius: 1rem;
  width: 100%;
  max-height: 250px;
  padding: .5rem 1.25rem;
  position: absolute;
  top: 65px;
  overflow-y: scroll;
}

.account-custom-select .body .search {
  background-color: rgba(255, 255, 255, .3);
  border-radius: 1rem;
  align-items: center;
  gap: 10px;
  width: 100%;
  margin: .5rem 0;
  padding: .5rem 1.25rem;
  display: flex;
}

.account-custom-select .body .search input {
  color: #fff;
  background-color: rgba(0, 0, 0, 0);
  width: 100%;
}

.account-custom-select .body .search input:focus-visible {
  outline: none !important;
}

.account-custom-select .body ul li {
  cursor: pointer;
  border-radius: 8px;
  padding: 10px;
  border-bottom: 0 !important;
  margin-bottom: 0 !important;
}

.account-custom-select .body ul li:hover {
  background-color: rgba(255, 255, 255, .3);
}

.account-custom-select.simple .header {
  background-color: #f2f2f2;
  border: 1px solid rgba(0, 0, 0, .2);
  border-radius: 10px;
  min-height: 34px;
  padding: 7px 10px;
}

.account-custom-select.simple .header span {
  font-size: 14px;
  font-weight: 600;
  color: #000 !important;
}

.account-custom-select.simple .body {
  background-color: #fff;
  border: 1px solid rgba(6, 6, 6, .1);
  border-radius: .94rem;
  width: 396px;
  max-height: 200px;
  padding: 10px 12px;
  top: 45px;
  right: 0;
}

@media only screen and (max-width: 500px) {
  .account-custom-select.simple .body {
    width: auto;
  }
}

.account-custom-select.simple .body.align-left {
  left: 0;
  right: auto;
}

.account-custom-select.simple .body.align-right {
  left: auto;
  right: 0;
}

.account-custom-select.simple .body::-webkit-scrollbar {
  display: block;
}

.account-custom-select.simple .body .option-heading {
  align-items: center;
  gap: 5px;
  margin: 6px 0 2px;
  display: flex;
}

.account-custom-select.simple .body .option-heading p {
  color: #000;
  font-size: 14px;
  font-weight: 700;
}

.account-custom-select.simple .body ul li {
  border-bottom: 1px solid rgba(0, 0, 0, .2);
  padding: 4px 0;
  font-size: 14px;
  font-weight: 600;
  border-radius: 0 !important;
}

.account-custom-select.simple .body ul li:hover {
  background-color: rgba(211, 211, 211, .165);
}

.width-autofit {
  width: -moz-fit-content !important;
  width: fit-content !important;
}

.text_00ADEF {
  color: #00adef !important;
}

input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.switch {
  width: 50px;
  height: 28px;
  display: inline-block;
  position: relative;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  cursor: pointer;
  background-color: #fff;
  border-radius: 34px;
  transition: all .4s;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.slider:before {
  content: "";
  background-color: #9c9a9f;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  transition: all .4s;
  position: absolute;
  bottom: 3px;
  left: 3px;
}

input:checked + .slider {
  background-color: #0099d1;
}

input:checked + .slider:before {
  background-color: #fff;
  transform: translateX(22px);
}

.modal_overlay {
  z-index: 9998;
  background-color: rgba(0, 0, 0, .6);
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}

.modal_overlay .search_section, .modal_overlay .modal-body {
  z-index: 1000;
  background-color: #031940;
  border: 1px solid #00adef;
  border-radius: 15px;
  width: 1000px;
  max-height: 90vh;
  padding: 20px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@media (max-width: 1023px) {
  .modal_overlay .search_section, .modal_overlay .modal-body {
    width: 90%;
  }
}

.modal_overlay .search_section h4, .modal_overlay .modal-body h4 {
  margin-bottom: 20px;
  font-size: 28px;
  font-weight: 400;
}

.modal_overlay .search_section .search_header, .modal_overlay .modal-body .search_header {
  margin-bottom: 20px;
}

.modal_overlay .search_section .search_header .closing_Section, .modal_overlay .modal-body .search_header .closing_Section {
  flex-shrink: 0;
  align-items: center;
  gap: 1rem;
  height: 100%;
  display: flex;
}

@media (max-width: 1023px) {
  .modal_overlay .search_section .search_header .closing_Section, .modal_overlay .modal-body .search_header .closing_Section {
    padding-right: 8px;
  }
}

.modal_overlay .search_section .search_header .closing_Section img, .modal_overlay .modal-body .search_header .closing_Section img {
  cursor: pointer;
}

.modal_overlay .search_section .search_header .search, .modal_overlay .modal-body .search_header .search {
  height: 100%;
}

.modal_overlay .search_section .search_header p, .modal_overlay .modal-body .search_header p {
  white-space: nowrap;
  font-size: 18px;
  font-weight: 600;
}

.modal_overlay .search_section .search_header span, .modal_overlay .modal-body .search_header span {
  white-space: nowrap;
  color: rgba(255, 255, 255, .6);
}

.modal_overlay .search_section .search_header .btn-style, .modal_overlay .modal-body .search_header .btn-style {
  min-width: 100%;
}

.modal_overlay .search_section .search_header button span, .modal_overlay .modal-body .search_header button span {
  color: #fff !important;
}

.modal_overlay .search_section .search, .modal_overlay .modal-body .search {
  background-color: rgba(255, 255, 255, .2);
  border-radius: 15px;
  gap: 10px;
  width: 100%;
  padding: 10px 20px;
  display: flex;
}

.modal_overlay .search_section .search input, .modal_overlay .modal-body .search input {
  color: rgba(255, 255, 255, .6);
  background-color: rgba(0, 0, 0, 0);
  width: 100%;
  height: 100%;
}

.modal_overlay .search_section .search input:focus, .modal_overlay .modal-body .search input:focus {
  box-shadow: none;
  outline: 0;
}

@keyframes fadeOut {
  0% {
    opacity: .9;
  }

  50% {
    opacity: 1;
  }

  90% {
    opacity: .9;
  }

  100% {
    opacity: 0;
  }
}

.modal_overlay .search_section .modal-footer-btn, .modal_overlay .modal-body .modal-footer-btn {
  font-size: 18px;
  min-height: 40px !important;
}

/*# sourceMappingURL=src_css_account_AccountLayout_scss_css_e59ae46c._.single.css.map*/