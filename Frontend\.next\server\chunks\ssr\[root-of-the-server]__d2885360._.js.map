{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Auth%29/security-check/SecurityCheck.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Auth)/security-check/SecurityCheck.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Auth)/security-check/SecurityCheck.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAkT,GAC/U,gFACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Auth%29/security-check/SecurityCheck.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Auth)/security-check/SecurityCheck.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Auth)/security-check/SecurityCheck.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA8R,GAC3T,4DACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Auth%29/security-check/page.js"], "sourcesContent": ["import { Suspense } from \"react\";\r\nimport SecurityCheck from \"./SecurityCheck\";\r\n\r\nexport default function Page() {\r\n    return (\r\n        <Suspense fallback={<div>Loading...</div>}>\r\n            <SecurityCheck />\r\n        </Suspense>\r\n    );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS;IACpB,qBACI,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;sBAAI;;;;;;kBACrB,cAAA,8OAAC,4JAAA,CAAA,UAAa;;;;;;;;;;AAG1B", "debugId": null}}]}