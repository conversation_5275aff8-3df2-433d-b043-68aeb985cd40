{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/category/CategoryClient.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/category/CategoryClient.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/category/CategoryClient.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA6S,GAC1U,2EACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/category/CategoryClient.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/category/CategoryClient.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/category/CategoryClient.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/category/page.js"], "sourcesContent": ["import CategoryClient from \"./CategoryClient\";\r\nimport { cookies } from \"next/headers\";\r\nimport JsonLdSchema, { generateCollectionPageSchema, generateBreadcrumbListSchema, generateCategoryBreadcrumbs } from \"@/Seo/Schema/JsonLdSchema\";\r\n\r\nexport default async function CategoryPage({ params, searchParams }) {\r\n  const slug = searchParams?.slug || null;\r\n  const page = parseInt(params?.id) || 1;\r\n  const cookieStore = cookies();\r\n  const key = cookieStore.get(\"categorySearchKey\")?.value || \"\";\r\n\r\n  const canonicalLink =\r\n    page === 1\r\n      ? `https://www.tradereply.com/category`\r\n      : `https://www.tradereply.com/category/page/${page}`;\r\n\r\n  let data = {\r\n    allcategories: [],\r\n    articles: [],\r\n    meta: {},\r\n    selected_category: null,\r\n  };\r\n\r\n  let categoryPagination = {};\r\n  let nextLink = null;\r\n\r\n  try {\r\n    const query = new URLSearchParams({ slug, key, page }).toString();\r\n    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || '';\r\n    const res = await fetch(`${apiBase}/category?${query}`, {\r\n      method: \"GET\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (!res.ok) {\r\n      throw new Error(\"Failed to fetch category data\");\r\n    }\r\n\r\n    const response = await res.json();\r\n    data = response.data;\r\n    console.log(\"API RESPONSE (server):\", JSON.stringify(response?.data, null, 2));\r\n\r\n    categoryPagination = data.meta;\r\n    if (categoryPagination?.current_page < categoryPagination?.total) {\r\n      nextLink = `https://www.tradereply.com/category/page/${categoryPagination.current_page + 1}`;\r\n    }\r\n\r\n  } catch (error) {\r\n    console.error(\"Failed to fetch category data:\", error);\r\n  }\r\n\r\n  const isSearch = key?.trim() !== \"\";\r\n  console.log(data.articles, \"test\");\r\n  const metaArray = {\r\n    title: \"TradeReply Categories | Explore Trading Content\",\r\n    description: \"Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.\",\r\n    og_title: \"TradeReply Categories | Explore Trading Content\",\r\n    og_description: \"Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.\",\r\n    og_site_name: \"TradeReply\",\r\n    twitter_title: \"TradeReply Categories | Explore Trading Content\",\r\n    twitter_description: \"Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.\",\r\n    noindex: isSearch,\r\n    ...(isSearch ? {} : { canonical_link: canonicalLink }),\r\n    rel_next: nextLink,\r\n  };\r\n  const categorySchemas = !isSearch ? [\r\n    generateCollectionPageSchema({\r\n      name: \"TradeReply Categories\",\r\n      description: \"Explore curated trading content and educational resources on TradeReply.com\",\r\n      url: canonicalLink,\r\n      articles: data.articles,\r\n      currentPage: page\r\n    }),\r\n    generateBreadcrumbListSchema({\r\n      items: generateCategoryBreadcrumbs(\"Categories\", page > 1 ? page : null)\r\n    })\r\n  ] : [];\r\n  // Generate JSON-LD schemas only when NOT in search mode\r\n  // let categorySchemas = [];\r\n  // if (!isSearch && data.articles && data.articles.length > 0) {\r\n  //   // Generate CollectionPage schema\r\n  //   const categoryName = data.selected_category?.title || \"Latest Articles\";\r\n  //   const collectionPageSchema = generateCollectionPageSchema({\r\n  //     name: categoryName,\r\n  //     description: \"Explore curated trading content and educational resources on TradeReply.com\",\r\n  //     url: canonicalLink,\r\n  //     articles: data.articles,\r\n  //     currentPage: page\r\n  //   });\r\n\r\n  //   // Generate BreadcrumbList schema\r\n  //   const breadcrumbItems = generateCategoryBreadcrumbs(categoryName, page > 1 ? page : null);\r\n  //   const breadcrumbSchema = generateBreadcrumbListSchema({\r\n  //     items: breadcrumbItems\r\n  //   });\r\n\r\n  //   // Add schemas to array (only if they were generated successfully)\r\n  //   if (collectionPageSchema) categorySchemas.push(collectionPageSchema);\r\n  //   if (breadcrumbSchema) categorySchemas.push(breadcrumbSchema);\r\n  // }\r\nconsole.log(\"categorySchemas\", categorySchemas);\r\n  return (\r\n    <>\r\n       <JsonLdSchema schemas={categorySchemas} />\r\n      <CategoryClient\r\n        initialData={data}\r\n        slug={slug}\r\n        keyWord={key}\r\n        currentPage={page}\r\n        metaArray={metaArray}\r\n      />\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,eAAe,aAAa,EAAE,MAAM,EAAE,YAAY,EAAE;IACjE,MAAM,OAAO,cAAc,QAAQ;IACnC,MAAM,OAAO,SAAS,QAAQ,OAAO;IACrC,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,MAAM,YAAY,GAAG,CAAC,sBAAsB,SAAS;IAE3D,MAAM,gBACJ,SAAS,IACL,CAAC,mCAAmC,CAAC,GACrC,CAAC,yCAAyC,EAAE,MAAM;IAExD,IAAI,OAAO;QACT,eAAe,EAAE;QACjB,UAAU,EAAE;QACZ,MAAM,CAAC;QACP,mBAAmB;IACrB;IAEA,IAAI,qBAAqB,CAAC;IAC1B,IAAI,WAAW;IAEf,IAAI;QACF,MAAM,QAAQ,IAAI,gBAAgB;YAAE;YAAM;YAAK;QAAK,GAAG,QAAQ;QAC/D,MAAM,UAAU,6DAAwC;QACxD,MAAM,MAAM,MAAM,MAAM,GAAG,QAAQ,UAAU,EAAE,OAAO,EAAE;YACtD,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,IAAI,EAAE,EAAE;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,MAAM,WAAW,MAAM,IAAI,IAAI;QAC/B,OAAO,SAAS,IAAI;QACpB,QAAQ,GAAG,CAAC,0BAA0B,KAAK,SAAS,CAAC,UAAU,MAAM,MAAM;QAE3E,qBAAqB,KAAK,IAAI;QAC9B,IAAI,oBAAoB,eAAe,oBAAoB,OAAO;YAChE,WAAW,CAAC,yCAAyC,EAAE,mBAAmB,YAAY,GAAG,GAAG;QAC9F;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kCAAkC;IAClD;IAEA,MAAM,WAAW,KAAK,WAAW;IACjC,QAAQ,GAAG,CAAC,KAAK,QAAQ,EAAE;IAC3B,MAAM,YAAY;QAChB,OAAO;QACP,aAAa;QACb,UAAU;QACV,gBAAgB;QAChB,cAAc;QACd,eAAe;QACf,qBAAqB;QACrB,SAAS;QACT,GAAI,WAAW,CAAC,IAAI;YAAE,gBAAgB;QAAc,CAAC;QACrD,UAAU;IACZ;IACA,MAAM,kBAAkB,CAAC,WAAW;QAClC,CAAA,GAAA,oIAAA,CAAA,+BAA4B,AAAD,EAAE;YAC3B,MAAM;YACN,aAAa;YACb,KAAK;YACL,UAAU,KAAK,QAAQ;YACvB,aAAa;QACf;QACA,CAAA,GAAA,oIAAA,CAAA,+BAA4B,AAAD,EAAE;YAC3B,OAAO,CAAA,GAAA,oIAAA,CAAA,8BAA2B,AAAD,EAAE,cAAc,OAAO,IAAI,OAAO;QACrE;KACD,GAAG,EAAE;IACN,wDAAwD;IACxD,4BAA4B;IAC5B,gEAAgE;IAChE,sCAAsC;IACtC,6EAA6E;IAC7E,gEAAgE;IAChE,0BAA0B;IAC1B,kGAAkG;IAClG,0BAA0B;IAC1B,+BAA+B;IAC/B,wBAAwB;IACxB,QAAQ;IAER,sCAAsC;IACtC,+FAA+F;IAC/F,4DAA4D;IAC5D,6BAA6B;IAC7B,QAAQ;IAER,uEAAuE;IACvE,0EAA0E;IAC1E,kEAAkE;IAClE,IAAI;IACN,QAAQ,GAAG,CAAC,mBAAmB;IAC7B,qBACE;;0BACG,8OAAC,oIAAA,CAAA,UAAY;gBAAC,SAAS;;;;;;0BACxB,8OAAC,oJAAA,CAAA,UAAc;gBACb,aAAa;gBACb,MAAM;gBACN,SAAS;gBACT,aAAa;gBACb,WAAW;;;;;;;;AAInB", "debugId": null}}]}