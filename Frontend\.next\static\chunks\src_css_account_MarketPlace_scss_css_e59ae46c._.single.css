/* [project]/src/css/account/MarketPlace.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

@media (max-width: 550px) {
  .container {
    padding: 0 !important;
  }
}

.account_search {
  background-color: rgba(255, 255, 255, .2);
  border-radius: 15px;
  gap: 16px;
  width: 100%;
  height: 72px;
  margin-bottom: 26px;
  padding: 10px 20px;
  display: flex;
}

.account_search input {
  color: rgba(255, 255, 255, .6);
  background-color: rgba(0, 0, 0, 0);
  width: 100%;
  height: 100%;
  font-size: 20px;
  font-weight: 600;
}

.account_search input:focus {
  box-shadow: none;
  outline: 0;
}

.account_card_btnArrow {
  background-color: #00adef;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  min-width: 30px;
  max-width: 30px;
  min-height: 29px;
  max-height: 29px;
  display: flex;
}

.account_card_btnArrow svg {
  transition: all .3s ease-in-out;
  color: #fff !important;
  margin-left: 0 !important;
}

.account_card_btnArrow svg path {
  fill: #fff !important;
}

@media (max-width: 767px) {
  .account_card_btnArrow svg {
    height: 10px !important;
  }
}

.account_card_btnArrow:hover {
  background-color: #0099d1;
}

.account_card_information {
  color: #000 !important;
}

.account_card_information .main_inform {
  gap: 20px;
  display: flex;
}

.account_card_information h4 {
  font-size: 20px;
  font-weight: 600;
  color: #000 !important;
}

@media (max-width: 991px) {
  .account_card_information h4 {
    font-size: 15px;
  }
}

.account_card_information .mail {
  margin-bottom: 7px;
  font-family: Gilroy-Semibold, sans-serif;
  font-size: 16px;
  font-weight: 400;
  color: #000 !important;
}

.account_card_information .location, .account_card_information .transaction, .account_card_information .active_sign, .account_card_information .since {
  gap: 9px;
  margin-bottom: 6px;
  font-family: Gilroy-Semibold, sans-serif;
  font-size: 14px;
  font-weight: 400;
  display: flex;
  color: #000 !important;
}

.account_card_information .active_sign span {
  background: #d9d9d9;
  border-radius: 50%;
  align-self: center;
  width: 12px;
  height: 12px;
}

.account_card_information .profile_photo {
  background-color: rgba(0, 0, 0, .1);
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 100px;
  height: 100px;
  padding: 20px;
  display: flex;
}

.account_card_information .profile_updateBtn {
  color: #00adef;
  cursor: pointer;
  margin-top: 5px;
  font-size: 14px;
  font-weight: bold;
}

.account_card_information .round-bluefill-btn {
  color: #fff;
  background-color: #00adef;
  border-radius: 15px;
  width: 100%;
  padding: 5px 15px;
  font-size: 14px;
  font-weight: 600;
  transition: all .3s ease-in-out;
  position: absolute;
  bottom: 0;
}

.account_card_information .round-bluefill-btn:hover {
  background-color: #0099d1;
}

.account_card_about {
  color: #000 !important;
}

.account_card_about .para_desc {
  margin-bottom: 7px;
  font-family: Gilroy-Semibold, sans-serif;
  font-size: 14px;
  font-weight: 400;
  line-height: 26px;
  color: #000 !important;
}

.account_card_about .website-link {
  color: #00adef;
  word-break: break-all;
  font-size: 14px;
  font-weight: 700;
}

.account_card_about .website-link:hover {
  color: #fea500;
}

.account_card_about .table_form_textarea {
  color: #fff;
  background-color: #4f5e7a;
  border-radius: 6px;
  width: 100%;
  padding: 5px 8px;
  font-size: 14px;
  font-weight: 600;
}

.account_card_about .table_form_textarea:focus {
  background-color: #576887;
  outline: none;
}

.account_card_about .character-count {
  text-align: end;
  color: #4f5e7a;
  margin-top: 2px;
  font-size: 14px;
}

.account_card_followers, .account_card_following {
  color: #000 !important;
}

.account_card_followers .main_inform, .account_card_following .main_inform {
  align-items: center;
  gap: 12px;
  margin-bottom: 6px;
  display: flex;
}

.account_card_followers h6, .account_card_following h6 {
  color: #00adef;
  font-size: 16px;
  font-weight: 600;
}

.account_card_followers .profile_photo, .account_card_following .profile_photo {
  background-color: rgba(0, 0, 0, .1);
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  display: flex;
  flex-shrink: 0 !important;
}

.account_card_followers .profile_photo img, .account_card_following .profile_photo img {
  width: 20px !important;
  height: 20px !important;
}

.account_card_followers .unFollow_status, .account_card_following .unFollow_status {
  cursor: pointer;
  background-color: #ff696a;
  border-radius: 50px;
  flex-shrink: 0;
  align-items: center;
  height: 43px;
  padding: 10px 15px;
  font-size: 16px;
  font-weight: 600;
  display: flex;
}

.account_card_followers .unFollow_status:hover, .account_card_following .unFollow_status:hover {
  background-color: #fe5252;
}

.account_card_marketplace {
  color: #000 !important;
}

.account_card_marketplace .mini_card {
  border: 1px solid rgba(6, 6, 6, .4);
  border-radius: 15px;
  margin-bottom: 10px;
  padding: 10px;
}

.account_card_marketplace .main_inform {
  align-items: center;
  gap: 10px;
  margin-bottom: 6px;
  display: flex;
}

.account_card_marketplace .star-rating {
  font-size: 14px;
  font-weight: 600;
}

.account_card_marketplace .most_recent, .account_card_marketplace .time {
  font-size: 14px;
  font-weight: 600;
  color: #000 !important;
}

.account_card_marketplace .profile_photo {
  background-color: rgba(0, 0, 0, .1);
  border-radius: 50%;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  width: 50px;
  height: 50px;
  padding: 15px;
  display: flex;
}

.account_card_marketplace h6 {
  font-family: Gilroy-Semibold, sans-serif;
  font-size: 18px;
  font-weight: 400;
  color: #000 !important;
}

.account_card_marketplace .mini_sc_title {
  display: none;
}

@media (max-width: 767px) {
  .account_card_marketplace h6 {
    display: none;
  }

  .account_card_marketplace .mini_sc_title {
    display: block;
  }
}

.account_card_marketplace .small_tag {
  font-size: 15px;
  font-weight: 600;
  color: #00adef !important;
}

.account_card_marketplace .time {
  white-space: nowrap;
}

.account_card_marketplace .thumbs_text {
  font-family: Gilroy-Semibold, sans-serif;
  font-size: 14px;
  font-weight: 400;
  color: #000 !important;
}

.account_card_active_listing {
  color: #000 !important;
}

.account_card_active_listing .mini_card {
  border: 1px solid rgba(6, 6, 6, .4);
  border-radius: 15px;
  margin-bottom: 10px;
  padding: 10px;
}

.account_card_active_listing .main_inform {
  align-items: center;
  gap: 10px;
  margin-bottom: 6px;
  display: flex;
}

.account_card_active_listing .star-rating {
  font-size: 14px;
  font-weight: 600;
}

.account_card_active_listing .most_recent {
  font-size: 14px;
  font-weight: 600;
  color: #000 !important;
}

.account_card_active_listing .respon_sell_feedback {
  align-items: start;
  gap: 17px;
}

@media (max-width: 900px) {
  .account_card_active_listing .respon_sell_feedback {
    display: block;
  }

  .account_card_active_listing .respon_sell_feedback h6 {
    margin-top: 10px;
  }

  .account_card_active_listing .respon_sell_feedback .activeListing_photo {
    max-width: 100%;
    height: 150px;
  }
}

.account_card_active_listing .activeListing_photo {
  aspect-ratio: 1;
  border-radius: 5px;
  flex-shrink: 0;
  width: 100%;
  max-width: 158px;
  overflow: hidden;
}

.account_card_active_listing .activeListing_photo img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.account_card_active_listing h6 {
  font-family: Gilroy-Semibold, sans-serif;
  font-size: 18px;
  font-weight: 400;
  color: #000 !important;
}

.account_card_active_listing .inner_price_text {
  font-size: 15px;
  font-weight: 600;
  color: #000 !important;
}

.account_card_active_listing .round-border-btn, .account_card_active_listing .rounded-border-btn {
  background-color: rgba(0, 0, 0, .05);
  border: 1px solid rgba(0, 0, 0, .2);
  border-radius: 50px;
  gap: 5px;
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
  transition: all .4s ease-in-out;
  display: flex;
}

.account_card_active_listing .round-border-btn:hover, .account_card_active_listing .rounded-border-btn:hover {
  border: 1px solid rgba(0, 0, 0, .48);
}

.account_card_active_listing .rounded-border-btn {
  padding: 10px 18px !important;
}

.account_card_active_listing .round-bluefill-btn {
  color: #fff;
  background-color: #00adef;
  border-radius: 50px;
  padding: 5px;
  font-size: 14px;
  font-weight: 600;
  transition: all .3s ease-in-out;
}

.account_card_active_listing .round-bluefill-btn:hover {
  background-color: #0099d1;
}

.account_card_active_listing .dropdownlist {
  align-items: center;
  gap: 12px;
  padding-top: 7px;
  padding-bottom: 7px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.account_card_active_listing .dropdownlist img {
  width: 18px !important;
  min-width: 18px !important;
  height: 18px !important;
}

.account_card_active_listing .dropdownlist span {
  text-align: left;
  flex: 1;
}

.account_card_dash_listings {
  color: #000 !important;
}

.account_card_dash_listings .mini_card {
  border: 1px solid rgba(6, 6, 6, .4);
  border-radius: 15px;
  margin-bottom: 10px;
  padding: 10px;
}

.account_card_dash_listings .main_inform {
  align-items: center;
  gap: 10px;
  margin-bottom: 6px;
  display: flex;
}

.account_card_dash_listings .star-rating {
  font-size: 14px;
  font-weight: 600;
}

.account_card_dash_listings .most_recent {
  font-size: 14px;
  font-weight: 600;
  color: #000 !important;
}

.account_card_dash_listings .respon_sell_feedback {
  align-items: start;
  gap: 17px;
}

@media (max-width: 767px) {
  .account_card_dash_listings .respon_sell_feedback {
    display: block;
  }

  .account_card_dash_listings .respon_sell_feedback h6 {
    margin-top: 10px;
  }

  .account_card_dash_listings .respon_sell_feedback .activeListing_photo {
    max-width: 100%;
    height: 150px;
  }
}

.account_card_dash_listings .activeListing_photo {
  aspect-ratio: 1;
  border-radius: 5px;
  flex-shrink: 0;
  width: 100%;
  max-width: 158px;
  overflow: hidden;
}

.account_card_dash_listings .activeListing_photo img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

.account_card_dash_listings h6 {
  font-family: Gilroy-Semibold, sans-serif;
  font-size: 18px;
  font-weight: 400;
  color: #000 !important;
}

.account_card_dash_listings .inner_price_text {
  font-size: 15px;
  font-weight: 600;
  color: #000 !important;
}

.account_card_dash_listings .actions_btn {
  flex-flow: row-reverse wrap;
  justify-content: space-between;
  width: 100%;
  margin-top: 50px;
  display: flex;
}

@media (max-width: 576px) {
  .account_card_dash_listings .actions_btn {
    margin-top: 12px;
  }
}

.account_card_dash_listings .actions_btn .first_part {
  gap: 8px;
  margin-bottom: 12px;
  display: flex;
  position: relative;
}

@media (max-width: 576px) {
  .account_card_dash_listings .actions_btn .first_part {
    justify-content: end;
  }
}

.account_card_dash_listings .actions_btn .second_part {
  gap: 8px;
  display: flex;
}

@media (max-width: 576px) {
  .account_card_dash_listings .actions_btn .second_part {
    justify-content: end;
    width: 100%;
  }

  .account_card_dash_listings .actions_btn .second_part button {
    width: 50%;
  }
}

.account_card_dash_listings .actions_btn .round-border-btn, .account_card_dash_listings .actions_btn .rounded-border-btn {
  background-color: rgba(0, 0, 0, .05);
  border: 1px solid rgba(0, 0, 0, .2);
  border-radius: 50px;
  justify-content: center;
  align-items: center;
  gap: 5px;
  width: auto;
  height: 35px;
  padding: 6px 12px;
  font-size: 14px;
  font-weight: 600;
  transition: all .4s ease-in-out;
  display: inline-flex;
}

.account_card_dash_listings .actions_btn .round-border-btn:hover, .account_card_dash_listings .actions_btn .rounded-border-btn:hover {
  border: 1px solid rgba(0, 0, 0, .48);
}

.account_card_dash_listings .actions_btn .rounded-border-btn {
  padding: 10px 15px !important;
}

.account_card_dash_listings .actions_btn .round-bluefill-btn {
  color: #fff;
  white-space: nowrap;
  background-color: #00adef;
  border-radius: 50px;
  justify-content: center;
  align-items: center;
  width: -moz-fit-content;
  width: fit-content;
  height: 35px;
  padding: 6px 18px;
  font-size: 14px;
  font-weight: 600;
  transition: all .3s ease-in-out;
  display: inline-flex;
}

.account_card_dash_listings .actions_btn .round-bluefill-btn:hover {
  background-color: #0099d1;
}

.account_card_dash_listings .actions_btn .dropdownlist {
  color: #000;
  align-items: center;
  gap: 14px;
  padding-top: 7px;
  padding-bottom: 7px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.account_card_dash_listings .actions_btn .dropdownlist img {
  width: 18px !important;
  min-width: 18px !important;
  height: 18px !important;
}

.account_card_dash_listings .actions_btn .dropdownlist span {
  text-align: left;
  flex: 1;
}

.account_card_dash_listings .actions_btn .dropdownlist:hover {
  color: #00adef;
}

.account_card_insight {
  color: #000 !important;
}

.account_card_insight .row {
  border-bottom: 1px solid rgba(0, 0, 0, .2);
}

.account_card_insight .row:last-child {
  border-bottom: none;
}

.account_card_insight .wrap_div {
  align-items: start;
  gap: 7px;
  padding: 10px 2px;
  display: flex;
}

@media (max-width: 323px) {
  .account_card_insight .wrap_div {
    gap: 5px;
  }
}

.account_card_insight .wrap_div img {
  flex-shrink: 0;
  margin-top: 2.3px;
}

.account_card_insight p {
  color: #000;
  align-items: start;
  gap: 10px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.account_card_disputes {
  color: #000 !important;
}

.account_card_disputes .lg_screen_table table {
  border-collapse: separate;
  table-layout: fixed;
  width: 100%;
  margin-bottom: 0;
}

.account_card_disputes .lg_screen_table table tr:last-child {
  border-bottom: 0 solid #fff;
}

.account_card_disputes .lg_screen_table table th {
  color: #fff;
  vertical-align: middle;
  background-color: #031940;
  border-left: 1.5px solid #fff;
  border-right: 4px solid #fff;
  border-radius: 15px;
  margin: 0 10px;
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
}

.account_card_disputes .lg_screen_table table th .th-inner {
  gap: 8px;
  display: flex;
}

.account_card_disputes .lg_screen_table table td {
  color: #000;
  word-wrap: break-word;
  border-right: 1px solid rgba(0, 0, 0, .2);
  margin-bottom: 5px;
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
}

.account_card_disputes .lg_screen_table table td:last-child {
  border-right: none;
}

.account_card_disputes .lg_screen_table table .view_res_btn {
  font-size: 14px;
  font-weight: 600;
  border-radius: 20px !important;
  width: 100% !important;
  min-width: 100% !important;
  height: auto !important;
  min-height: auto !important;
  padding: 8px 11px !important;
}

.account_card_disputes .lg_screen_table .bullet-points {
  align-items: start;
  gap: 8px;
  margin-bottom: 8px;
  display: flex;
}

.account_card_disputes .lg_screen_table .bullet-points img {
  flex-shrink: 0;
  margin-top: 7px;
}

.account_card_disputes .sm_screen_table .wrap-div {
  border-bottom: 1px solid rgba(0, 0, 0, .4);
  margin-bottom: 10px;
}

.account_card_disputes .sm_screen_table .wrap-div:last-child {
  border-bottom: none;
}

.account_card_disputes .sm_screen_table .row .colunm_head {
  color: #fff;
  background-color: #031940;
  border-radius: 15px;
  gap: 4px;
  margin-bottom: 5px;
  padding: 10px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
}

.account_card_disputes .sm_screen_table .row .colunm_value {
  color: #000;
  word-wrap: break-word;
  border-right: 1px solid rgba(0, 0, 0, .2);
  margin-top: 3px;
  margin-bottom: 3px;
  padding: 0 7px;
  font-size: 14px;
  font-weight: 600;
}

.account_card_disputes .sm_screen_table .row .colunm_value:last-child, .account_card_disputes .sm_screen_table .row .colunm_value:nth-child(4) {
  border: none;
}

.account_card_disputes .sm_screen_table .row .arrow-header {
  width: 30px;
}

.account_card_disputes .sm_screen_table .bullet-points {
  align-items: start;
  gap: 8px;
  margin-bottom: 8px;
  display: flex;
}

.account_card_disputes .sm_screen_table .bullet-points img {
  flex-shrink: 0;
  margin-top: 7px;
}

.account_card_disputes .no_disputes {
  color: #000;
  font-size: 18px;
  font-weight: 600;
}

.account_card_disputes .table_form_textarea {
  color: #fff;
  background-color: #4f5e7a;
  border-radius: 6px;
  width: 100%;
  padding: 5px 8px;
  font-size: 14px;
  font-weight: 600;
}

.account_card_disputes .table_form_textarea:focus {
  background-color: #576887;
  outline: none;
}

.account_card_disputes .character-count {
  text-align: end;
  color: #4f5e7a;
  margin-top: 2px;
  font-size: 14px;
}

.account_card_disputes .file-upload-wrapper {
  color: #000;
  cursor: pointer;
  text-align: center;
  background-color: #fff;
  border: 2px solid #ddd;
  border-radius: 9999px;
  width: 100%;
  height: 37px;
  padding: 6px 20px;
  font-size: 14px;
  font-weight: 600;
  transition: background-color .2s;
  display: inline-block;
}

.account_card_disputes .file-upload-wrapper:hover {
  background-color: #f0f0f0;
}

.account_card_disputes .file-upload-wrapper input[type="file"] {
  display: none;
}

.account_card_timeline {
  color: #000 !important;
}

.account_card_timeline h2 {
  color: #fff;
  background-color: #031940;
  border-radius: 15px;
  padding: 12px;
  font-size: 14px;
  font-weight: 600;
  display: inline-flex;
}

.account_card_timeline ul {
  margin: 13px 0;
  padding-left: 17px;
}

.account_card_timeline ul li {
  color: #000;
  border-radius: 15px;
  font-size: 14px;
  font-weight: 600;
  list-style-type: circle;
}

.account_card_create_listing .form-wrapper .form-input, .account_card_create_listing .form-wrapper .form-select, .account_card_create_listing .form-wrapper .form-textarea {
  color: #000;
  border: none;
  border-radius: 15px;
  width: 100%;
  margin-bottom: 12px;
  padding: 1rem;
  font-size: 18px;
}

@media (max-width: 550px) {
  .account_card_create_listing .form-wrapper .form-input, .account_card_create_listing .form-wrapper .form-select, .account_card_create_listing .form-wrapper .form-textarea {
    font-size: 14px;
  }
}

.account_card_create_listing .form-wrapper .form-input::placeholder, .account_card_create_listing .form-wrapper .form-select::placeholder, .account_card_create_listing .form-wrapper .form-textarea::placeholder {
  color: rgba(0, 0, 0, .8);
}

.account_card_create_listing .form-wrapper .form-input:focus, .account_card_create_listing .form-wrapper .form-select:focus, .account_card_create_listing .form-wrapper .form-textarea:focus {
  outline: none;
}

.account_card_create_listing .form-wrapper .tags-input {
  background: #fff;
  border-radius: 15px;
  flex-wrap: wrap;
  gap: 10px;
  margin-bottom: 12px;
  padding: .8rem 1rem;
  display: flex;
}

.account_card_create_listing .form-wrapper .tags-input input {
  color: #000;
  border: none;
  width: 100%;
  font-size: 18px;
}

@media (max-width: 501px) {
  .account_card_create_listing .form-wrapper .tags-input input {
    font-size: 15px;
  }
}

.account_card_create_listing .form-wrapper .tags-input input::placeholder {
  color: rgba(0, 0, 0, .8);
}

.account_card_create_listing .form-wrapper .tags-input input:focus {
  outline: none;
}

.account_card_create_listing .form-wrapper .tags-input .tag {
  color: #000;
  background-color: #e6e6e6;
  align-items: center;
  font-size: 18px;
  display: flex;
  border-radius: 50px !important;
  gap: 12px !important;
  padding: 5px 9px !important;
}

.account_card_create_listing .form-wrapper .tags-input .tag svg {
  cursor: pointer;
  font-weight: 700;
}

.account_card_create_listing .form-wrapper .checkbox-wrapper {
  align-items: center;
  gap: .6rem;
  margin-bottom: 11px;
  display: flex;
}

.account_card_create_listing .form-wrapper .checkbox-wrapper .custom_checkbox_input {
  width: 20px;
  height: 20px;
}

.account_card_create_listing .form-wrapper .checkbox-wrapper .custom_checkbox_input:checked {
  background-color: #00adef !important;
  border: 1px solid #00adef !important;
}

.account_card_create_listing .form-wrapper .file-upload-wrapper {
  color: #000;
  cursor: pointer;
  background-color: #fff;
  border: 2px solid #ddd;
  border-radius: 9999px;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 46px;
  margin-bottom: 12px;
  padding: 6px 20px;
  font-size: 16px;
  font-weight: 600;
  transition: background-color .2s;
  display: flex;
}

@media (max-width: 501px) {
  .account_card_create_listing .form-wrapper .file-upload-wrapper {
    font-size: 15px;
  }
}

.account_card_create_listing .form-wrapper .file-upload-wrapper:hover {
  background-color: #f0f0f0;
}

.account_card_create_listing .form-wrapper .file-upload-wrapper input[type="file"] {
  display: none;
}

.account_card_create_listing .form-wrapper .character-count {
  color: rgba(0, 0, 0, .8);
  font-size: .9rem;
  font-weight: 600;
  position: absolute;
  bottom: 18px;
  right: 11px;
}

.account_card_create_listing .form-wrapper .outer-character-count {
  text-align: end;
  color: rgba(255, 255, 255, .8);
  margin-bottom: 12px;
  font-size: .9rem;
  font-weight: 600;
}

.account_card_create_listing .form-wrapper .upload-container {
  height: 100%;
}

@media (max-width: 991px) {
  .account_card_create_listing .form-wrapper .upload-container {
    margin-bottom: 20px;
  }
}

.account_card_create_listing .form-wrapper .upload-container .upload-box {
  text-align: center;
  cursor: pointer;
  background: #fff;
  border-radius: 12px;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 87%;
  padding: .5rem;
  font-size: 20px;
  font-weight: 600;
  display: flex;
}

@media (min-width: 767px) and (max-width: 991px) {
  .account_card_create_listing .form-wrapper .upload-container .upload-box {
    height: 300px;
  }
}

@media (max-width: 768px) {
  .account_card_create_listing .form-wrapper .upload-container .upload-box {
    height: 200px;
  }
}

.account_card_create_listing .form-wrapper .upload-container .upload-box .upload-placeholder img {
  margin: 0 auto;
}

.account_card_create_listing .form-wrapper .upload-container .upload-box .upload-placeholder p {
  color: #000;
  margin: .35rem 0 0;
  font-size: 20px;
  font-weight: 600;
}

.account_card_create_listing .form-wrapper .upload-container .upload-box .upload-placeholder .sub-text {
  margin: 0;
  font-size: 15px !important;
}

.account_card_create_listing .form-wrapper .upload-container .upload-box .preview {
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  display: flex;
  position: relative;
  overflow-y: hidden;
}

.account_card_create_listing .form-wrapper .upload-container .upload-box .preview img {
  object-fit: contain;
  border-radius: 8px;
  width: 100%;
  height: 80%;
}

.account_card_create_listing .form-wrapper .upload-container .upload-box .preview .remove-btn {
  color: #fff;
  cursor: pointer;
  background: #ff4d4f;
  border: none;
  border-radius: 50%;
  width: 25px;
  height: 25px;
  font-size: 14px;
  font-weight: bold;
  position: absolute;
  top: 55%;
  right: 46%;
  transform: translate(-50%, -50%);
}

.account_card_create_listing .form-wrapper .upload-container .image-count {
  color: #fff;
  margin: .5rem 0;
  font-size: 15px;
  font-weight: 600;
}

.account_card_create_listing .form-wrapper .form-multi-select {
  color: #000;
  border-radius: 15px;
  font-size: 18px;
}

.account_card_create_listing .form-wrapper .select__control {
  border-radius: 15px;
  padding: 9px;
  font-size: 18px;
}

.account_card_create_listing .form-wrapper .select__placeholder {
  color: #000 !important;
}

.account_card_create_listing .form-wrapper .select__multi-value {
  border-radius: 20px;
  padding: 2px 6px;
  font-size: 18px;
}

.account_card_create_listing .form-wrapper .select__multi-value__label {
  border-radius: 5px;
  font-size: 18px;
}

.account_card_create_listing .form-wrapper .select__multi-value__remove {
  color: #000;
  border-radius: 50%;
  padding-left: 9px;
  padding-right: 9px;
}

.account_card_create_listing .form-wrapper .select__multi-value__remove:hover {
  background-color: #d3d3d3;
  border-radius: 50%;
  padding-left: 9px;
  padding-right: 9px;
}

@media not (max-width: 991px) {
  .listing-rightside {
    margin-top: 36px;
  }
}

.create-listing-categories {
  position: relative;
}

.create-listing-categories .field {
  cursor: pointer;
  color: #000;
  background-color: #fff;
  border: none;
  border-radius: 15px;
  width: 100%;
  margin-bottom: 12px;
  padding: 1rem;
  font-size: 18px;
  display: flex;
}

@media (max-width: 550px) {
  .create-listing-categories .field {
    font-size: 14px;
  }
}

.create-listing-categories .category-toggle {
  width: 100%;
}

.create-listing-categories .dropdown {
  color: #000;
  z-index: 999;
  background-color: #fff;
  border: none;
  border-radius: 15px;
  width: 100%;
  font-size: 18px;
  position: absolute;
  top: 65px;
  left: 0;
  box-shadow: 0 13px 27px -5px rgba(50, 50, 93, .25), 0 8px 16px -8px rgba(0, 0, 0, .3);
}

.create-listing-categories .dropdown .dropdown-header {
  cursor: pointer;
  border-bottom: 1px solid #e5e7eb;
  align-items: center;
  padding: .5rem;
  display: flex;
}

.create-listing-categories .dropdown .dropdown-header .dropdown-title {
  text-align: center;
  width: 100%;
}

.create-listing-categories .dropdown ul li {
  cursor: pointer;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  display: flex;
}

@media (max-width: 550px) {
  .create-listing-categories .dropdown ul li {
    padding: .75rem;
    font-size: 14px;
  }
}

.create-listing-categories .dropdown ul li:hover {
  background-color: #e6e6e6 !important;
}

.create-listing-categories .dropdown ul li:hover:first-child {
  border-radius: 15px 15px 0 0;
}

.create-listing-categories .dropdown ul li:hover:last-child {
  border-radius: 0 0 15px 15px;
}

.create-listing-categories .dropdown .sub_list li:hover:first-child {
  border-radius: 0 !important;
}

.create-listing-categories .selected-values-container .selected-values {
  color: #fff;
  background-color: #00adef;
  border-radius: 15px;
  justify-content: space-between;
  align-items: center;
  gap: 10px;
  width: 100%;
  margin-bottom: 12px;
  padding: 5px 15px;
  display: flex;
}

.create-listing-categories .selected-values-container .selected-values .values {
  align-items: center;
  gap: 8px;
  display: flex;
}

@media (max-width: 550px) {
  .create-listing-categories .selected-values-container .selected-values .values {
    display: block;
  }
}

.create-listing-categories .selected-values-container .selected-values .values span {
  align-items: center;
  gap: 5px;
  font-size: 14px;
  font-weight: 700;
  display: flex;
}

.create-listing-categories .selected-values-container .selected-values .remove-selected svg {
  cursor: pointer;
  width: 1.3rem;
  height: 1.3rem;
}

.create-listing-categories .selected-values-container .clear-all {
  color: #fff;
  background-color: #ff696a;
  border-radius: 15px;
  margin-bottom: 12px;
  padding: 5px 10px;
  font-size: 14px;
  font-weight: 700;
}

.create-listing-categories .selected-values-container .clear-all:hover {
  background-color: #e65f60;
}

.disabled {
  pointer-events: none;
  opacity: .5;
  cursor: not-allowed;
}

/*# sourceMappingURL=src_css_account_MarketPlace_scss_css_e59ae46c._.single.css.map*/