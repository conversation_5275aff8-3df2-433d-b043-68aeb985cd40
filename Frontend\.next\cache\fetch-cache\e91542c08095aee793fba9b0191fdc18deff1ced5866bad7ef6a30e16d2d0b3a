{"kind": "FETCH", "data": {"headers": {"cache-control": "no-cache, private", "connection": "close", "content-type": "application/json", "date": "Mon, 11 Aug 2025 12:28:13 GMT, Mon, 11 Aug 2025 12:28:13 GMT", "host": "127.0.0.1:8000", "vary": "Origin", "x-powered-by": "PHP/8.2.4", "x-ratelimit-limit": "500", "x-ratelimit-remaining": "499"}, "body": "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", "status": 200, "url": "http://127.0.0.1:8000/api/v1/featured-resource"}, "revalidate": 3600, "tags": []}