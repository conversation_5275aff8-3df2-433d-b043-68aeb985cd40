<?php

namespace App\Services;

use App\Models\TradeForm;
use App\Models\TradeFormSection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class TradeFormSectionService
{
    protected $formulas;
    protected $propagatableFields = [];
    protected $propagatableLastExitFields = [];

    public function __construct()
    {
        $jsonFile = public_path('TradeReply_Formulas.json');
        $this->formulas = json_decode(file_get_contents($jsonFile), true) ?? [];

        foreach ($this->formulas as $formula) {
            $scope = $formula['SCOPES']['TRANSACTION'] ?? null;
            if ($scope) {
                $field = strtolower($scope['DATABASE FIELD']);
                if (($scope['scope']['exit']['resolve'] ?? null) === 'linked_entry') {
                    $this->propagatableFields[] = $field;
                }
                if (($scope['scope']['entry']['resolve'] ?? null) === 'last_linked_exit') {
                    $this->propagatableLastExitFields[] = $field;
                }
            }
        }
    }

    public function validateRequest($request)
    {
        return Validator::make($request->all(), [
            'formKey' => 'required',
            'section' => 'required|string|in:overview,projection,outcome',
            'data' => 'required|array',
            'data.*.input' => 'required|string',
            'data.*.value' => 'nullable',
        ]);
    }

    public function findForm($formKey, $userId, $tradeAccountId)
    {
        return TradeForm::where('id', $formKey)
            ->whereHas('trade', fn ($q) => $q->where('user_id', $userId)->where('trade_account_id', $tradeAccountId)->whereNull('deleted_at'))
            ->first();
    }

    public function saveSectionData($form, $request)
    {
        $section = TradeFormSection::firstOrCreate(
            ['trade_form_id' => $form->id, 'section' => $request->section],
            ['data' => [], 'formula_data' => []]
        );

        $existingData = $section->data ?? [];
        $computedData = $section->computed_data ?? [];

        $newData = array_map(function ($item) use ($request, $computedData) {
            $input = $item['input'];
            $isExtra = $item['is_extra'] ?? false;
            $isResolved = $item['is_resolved'] ?? false;

            $value = $item['value'];

            if ($isExtra && !$isResolved && array_key_exists($input, $computedData)) {
                $value = $computedData[$input];
            }

            return [
                'input' => $input,
                'value' => $value,
                'isFormula' => $request->formulaModeInputs[$input] ?? ($item['isFormula'] ?? false),
                'is_extra' => $isExtra,
                'is_resolved' => $isResolved,
            ];
        }, $request->data);

        $mergedData = $this->mergeUniqueByInput($existingData, $newData);

        $section->update([
            'data' => $mergedData,
            'formula_data' => $request->formulaModeInputs,
        ]);

        return $section;
    }
    protected function mergeUniqueByInput($existing, $new)
    {
        $map = [];
        foreach ($existing as $item) {
            $key = strtolower($item['input']);
            $map[$key] = $item;
        }

        foreach ($new as $item) {
            $key = strtolower($item['input']);

            if (isset($map[$key])) {
                $existingItem = $map[$key];

                $merged = array_merge($existingItem, $item);

                $merged['is_extra'] =
                    (($existingItem['is_extra'] ?? false) || ($item['is_extra'] ?? false));

                if (!array_key_exists('is_resolved', $item)) {
                    $merged['is_resolved'] = $existingItem['is_resolved'] ?? false;
                }

                if (!array_key_exists('isFormula', $item)) {
                    $merged['isFormula'] = $existingItem['isFormula'] ?? false;
                }

                $map[$key] = $merged;
            } else {
                $map[$key] = $item;
            }
        }

        return array_values($map);
    }

    public function propagateData($form, $section, $request)
    {
        $type = $form->type;
        $sectionName = $request->section;

        if ($type === 'entry' && $sectionName === 'overview') {
            $this->propagateToLinkedExits($form, $request->data);
            $this->propagateGlobalToAllForms($form, $request->data);
        }

        if ($type === 'exit' && $sectionName === 'overview') {
            $this->propagateFromLinkedEntry($form, $section, $request->data);
            $this->propagateToLinkedEntry($form, $request->data);
        }
    }

    protected function propagateToLinkedExits($form, $newData)
    {
        foreach ($newData as $item) {
            if ($item['is_extra'] ?? false) {
                continue;
            }
            $input = strtolower($item['input']);
            $value = $item['value'];

            if (in_array($input, $this->propagatableFields)) {
                $linkedExits = TradeForm::where('trade_id', $form->trade_id)
                    ->where('type', 'exit')
                    ->get()
                    ->filter(function ($exitForm) use ($form) {
                        $overview = $exitForm->sections->firstWhere('section', 'overview');
                        $linked = collect($overview->data ?? [])->firstWhere('input', 'linked_entry')['value'] ?? null;
                        return (string)$linked === (string)$form->index;
                    });

                foreach ($linkedExits as $exitForm) {
                    $this->updateFormSectionValue($exitForm, $input, $value, true);
                }
            }

            if (in_array($input, $this->propagatableLastExitFields)) {
                $lastLinkedExit = TradeForm::where('trade_id', $form->trade_id)
                    ->where('type', 'exit')
                    ->get()
                    ->filter(fn ($exitForm) => (string) collect($exitForm->sections->firstWhere('section', 'overview')->data ?? [])->firstWhere('input', 'linked_entry')['value'] ?? null === (string)$form->index)
                    ->sortByDesc('id')->first();

                if ($lastLinkedExit) {
                    $this->updateFormSectionValue($lastLinkedExit, $input, $value, true);
                }
            }
        }
    }

    protected function propagateGlobalToAllForms($form, $newData)
    {
        if ($form->index !== 1) {
            return;
        }

        foreach ($newData as $item) {
            $input = strtolower($item['input']);
            $value = $item['value'];

            $isGlobal = collect($this->formulas)->contains(function ($formula) use ($input) {
                $scope = $formula['SCOPES']['TRANSACTION'] ?? [];
                return strtolower($scope['DATABASE FIELD'] ?? '') === $input &&
                    ($scope['field_behavior']['global_scope'] ?? false);
            });

            if ($isGlobal) {
                $otherForms = TradeForm::where('trade_id', $form->trade_id)->where('id', '!=', $form->id)->get();
                foreach ($otherForms as $f) {
                    $this->updateFormSectionValue($f, $input, $value, false, null, false);
                }
            }
        }
    }

    protected function propagateFromLinkedEntry($exitForm, $exitSection, $newData)
    {
        $linkedIndex = collect($newData)->firstWhere('input', 'linked_entry')['value'] ?? null;
        if (!$linkedIndex) {
            return;
        }

        $entryForm = TradeForm::where('trade_id', $exitForm->trade_id)->where('type', 'entry')->where('index', $linkedIndex)->first();
        if (!$entryForm) {
            return;
        }

        $entryData = collect(optional($entryForm->sections->firstWhere('section', 'overview'))->data ?? []);
        foreach ($this->propagatableFields as $field) {
            $entryItem = $entryData->firstWhere('input', $field);
            if ($entryItem) {
                $this->updateFormSectionValue($exitForm, $field, $entryItem['value'], true, $exitSection);
            }
        }
    }

    protected function propagateToLinkedEntry($exitForm, $newData)
    {
        $linkedIndex = collect($newData)->firstWhere('input', 'linked_entry')['value'] ?? null;
        if (!$linkedIndex) {
            return;
        }

        $entryForm = TradeForm::where('trade_id', $exitForm->trade_id)->where('type', 'entry')->where('index', $linkedIndex)->first();
        if (!$entryForm) {
            return;
        }

        foreach ($newData as $item) {
            if ($item['is_extra'] ?? false) {
                continue;
            }
            $input = strtolower($item['input']);
            $value = $item['value'];

            if (in_array($input, $this->propagatableLastExitFields)) {
                $isLastExit = TradeForm::where('trade_id', $exitForm->trade_id)
                    ->where('type', 'exit')
                    ->whereHas('sections', function ($query) use ($linkedIndex) {
                        $query->where('section', 'overview')
                            ->whereJsonContains('data', ['input' => 'linked_entry', 'value' => $linkedIndex]);
                    })
                    ->orderBy('index', 'desc')
                    ->first()->id === $exitForm->id;

                if ($isLastExit) {
                    $this->updateFormSectionValue($entryForm, $input, $value, true);
                }
            }
        }
    }

    protected function updateFormSectionValue($form, $input, $value, $isFormula = false, $section = null, $isResolved = true)
    {
        $section = $section ?? TradeFormSection::firstOrCreate(
            ['trade_form_id' => $form->id, 'section' => 'overview'],
            ['data' => [], 'formula_data' => []]
        );

        $data = collect($section->data ?? []);
        $updated = false;

        $existingItem = $data->firstWhere('input', $input);
        $existingIsExtra = (bool)($existingItem['is_extra'] ?? false);
        $existingIsResolved = (bool)($existingItem['is_resolved'] ?? false);

        if ($existingIsExtra && !$existingIsResolved) {
            $computedValue = optional($section->computed_data)[$input] ?? null;
            if (!is_null($computedValue)) {
                $value = $computedValue;
            }
        }

        $data = $data->map(function ($d) use ($input, $value, $isFormula, $isResolved, $existingIsExtra, &$updated) {
            if (strtolower($d['input']) === strtolower($input)) {
                $d['is_extra'] = ($d['is_extra'] ?? false) ? true : $existingIsExtra;

                $d['value'] = $value;
                $d['isFormula'] = $isFormula;
                if ($isResolved) {
                    $d['is_resolved'] = true;
                }
                $updated = true;
            }
            return $d;
        });

        if (!$updated) {
            $newItem = [
                'input'      => $input,
                'value'      => $value,
                'isFormula'  => $isFormula,
                'is_extra'   => $existingIsExtra ? true : false,
            ];
            if ($isResolved) {
                $newItem['is_resolved'] = true;
            }
            $data->push($newItem);
        }

        $section->update(['data' => $data->values()->all()]);
    }
}