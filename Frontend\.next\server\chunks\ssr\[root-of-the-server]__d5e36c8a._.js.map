{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/components/EducationContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/[detail]/components/EducationContent.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/[detail]/components/EducationContent.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoU,GACjW,kGACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/components/EducationContent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(Home)/education/[detail]/components/EducationContent.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(Home)/education/[detail]/components/EducationContent.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Header.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Header.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Header.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Header.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Header.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Header.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 82, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Footer.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Footer.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Footer.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA+R,GAC5T,6DACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Components/UI/Footer.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/Components/UI/Footer.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/Components/UI/Footer.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA2Q,GACxS,yCACA", "debugId": null}}, {"offset": {"line": 106, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/Layouts/HomeLayout.js"], "sourcesContent": ["// 'use client';\r\n\r\nimport \"bootstrap/dist/css/bootstrap.min.css\";\r\nimport { Fragment } from \"react\";\r\nimport Header from \"@/Components/UI/Header\";\r\nimport Footer from \"@/Components/UI/Footer\";\r\nimport { LanguageProvider } from \"@/context/LanguageContext\";\r\n\r\nconst HomeLayout = ({ children }) => {\r\n  return (\r\n    <Fragment>\r\n      <LanguageProvider>\r\n        <Header />\r\n        {/* Wrap main content in <main> for SEO & accessibility */}\r\n        <main className=\"main-content\">{children}</main>\r\n        <Footer />\r\n      </LanguageProvider>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default HomeLayout;\r\n"], "names": [], "mappings": "AAAA,gBAAgB;;;;;AAGhB;AACA;AACA;AACA;;;;;;;AAEA,MAAM,aAAa,CAAC,EAAE,QAAQ,EAAE;IAC9B,qBACE,8OAAC,qMAAA,CAAA,WAAQ;kBACP,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;;8BACf,8OAAC,iIAAA,CAAA,UAAM;;;;;8BAEP,8OAAC;oBAAK,WAAU;8BAAgB;;;;;;8BAChC,8OAAC,iIAAA,CAAA,UAAM;;;;;;;;;;;;;;;;AAIf;uCAEe", "debugId": null}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28Home%29/education/%5Bdetail%5D/page.js"], "sourcesContent": ["import { unstable_noStore as noStore } from 'next/cache';\r\nimport EducationContent from \"./components/EducationContent\";\r\nimport HomeLayout from \"@/Layouts/HomeLayout\";\r\n// import MetaHead from \"@/Seo/Meta/MetaHead\"; // (unused)\r\nimport { Container } from \"react-bootstrap\";\r\nimport JsonLdSchema from \"@/Seo/Schema/JsonLdSchema\";\r\n\r\n// --- Helper: keep outside the component ---\r\nfunction generateEducationArticleSchema(detailSlug, articleData, baseUrl = \"https://www.tradereply.com\") {\r\n  const created = articleData?.created_at\r\n    ? new Date(articleData.created_at).toISOString()\r\n    : new Date().toISOString();\r\n\r\n  const modified = articleData?.updated_at\r\n    ? new Date(articleData.updated_at).toISOString()\r\n    : created;\r\n\r\n  return {\r\n    \"@context\": \"https://schema.org\",\r\n    \"@type\": \"Article\",\r\n    \"mainEntityOfPage\": {\r\n      \"@type\": \"WebPage\",\r\n      \"@id\": `${baseUrl}/education/${detailSlug}`,\r\n    },\r\n    \"headline\": articleData?.title || \"\",\r\n    \"description\": articleData?.summary || \"\",\r\n    \"author\": { \"@type\": \"Organization\", \"name\": \"TradeReply\" },\r\n    \"publisher\": {\r\n      \"@type\": \"Organization\",\r\n      \"name\": \"TradeReply\",\r\n      \"logo\": {\r\n        \"@type\": \"ImageObject\",\r\n        \"url\": \"https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png\",\r\n      },\r\n    },\r\n    \"datePublished\": created,\r\n    \"dateModified\": modified,\r\n    \"articleSection\": \"Education\",\r\n    \"articleBody\": articleData?.articleBody || \"\",\r\n  };\r\n}\r\n\r\nasync function fetchEducationDetail(detail) {\r\n  const url = new URL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/article/education/${detail}`);\r\n  const res = await fetch(url.toString(), { cache: \"no-store\" });\r\n  if (!res.ok) throw new Error(`API error: ${res.status}`);\r\n  return res.json();\r\n}\r\n\r\nexport async function generateMetadata({ params }) {\r\n  noStore();\r\n  const detailSlug = params.detail;\r\n\r\n  // Wrap in try/catch so metadata doesn’t hard-fail if API hiccups\r\n  try {\r\n    const data = await fetchEducationDetail(detailSlug);\r\n    const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;\r\n\r\n    return {\r\n      title: `What is ${data?.data?.title} | TradeReply Education`,\r\n      description: data?.data?.summary,\r\n      openGraph: {\r\n        title: `What is ${data?.data?.title} | TradeReply Education`,\r\n        description: data?.data?.summary,\r\n        images: [{\r\n          url: data?.data?.feature_image_url || 'https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg',\r\n          width: 1200,\r\n          height: 630,\r\n        }],\r\n      },\r\n      twitter: {\r\n        title: `What is ${data?.data?.title} | TradeReply Education`,\r\n        description: data?.data?.summary,\r\n        site: '@JoinTradeReply',\r\n        images: [data?.data?.feature_image_url || 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'],\r\n      },\r\n      icons: {\r\n        icon: [\r\n          {\r\n            url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,\r\n            type: \"image/x-icon\",\r\n          },\r\n          {\r\n            url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,\r\n            type: \"image/svg+xml\",\r\n          },\r\n        ],\r\n      },\r\n    };\r\n  } catch {\r\n    return {\r\n      title: \"TradeReply Education\",\r\n      description: \"Explore trading education on TradeReply.\",\r\n    };\r\n  }\r\n}\r\n\r\nexport default async function EducationDetail({ params }) {\r\n  noStore();\r\n\r\n  const detailSlug = params.detail;\r\n  const data = await fetchEducationDetail(detailSlug);\r\n  const articleData = data.data;\r\n\r\n  const educationSchema = generateEducationArticleSchema(detailSlug, articleData);\r\n  const schemas = [educationSchema]; // 🔹 ensure it's an array\r\n\r\n  return (\r\n    <>\r\n      <JsonLdSchema schemas={schemas} />\r\n      <HomeLayout>\r\n        <Container>\r\n          <EducationContent\r\n            detailSlug={detailSlug}\r\n            articleData={articleData}\r\n            nextArticle={data.next_article}\r\n            avgProgress={data.avgProgress}\r\n          />\r\n        </Container>\r\n      </HomeLayout>\r\n    </>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AACA,0DAA0D;AAC1D;AACA;;;;;;;AAEA,6CAA6C;AAC7C,SAAS,+BAA+B,UAAU,EAAE,WAAW,EAAE,UAAU,4BAA4B;IACrG,MAAM,UAAU,aAAa,aACzB,IAAI,KAAK,YAAY,UAAU,EAAE,WAAW,KAC5C,IAAI,OAAO,WAAW;IAE1B,MAAM,WAAW,aAAa,aAC1B,IAAI,KAAK,YAAY,UAAU,EAAE,WAAW,KAC5C;IAEJ,OAAO;QACL,YAAY;QACZ,SAAS;QACT,oBAAoB;YAClB,SAAS;YACT,OAAO,GAAG,QAAQ,WAAW,EAAE,YAAY;QAC7C;QACA,YAAY,aAAa,SAAS;QAClC,eAAe,aAAa,WAAW;QACvC,UAAU;YAAE,SAAS;YAAgB,QAAQ;QAAa;QAC1D,aAAa;YACX,SAAS;YACT,QAAQ;YACR,QAAQ;gBACN,SAAS;gBACT,OAAO;YACT;QACF;QACA,iBAAiB;QACjB,gBAAgB;QAChB,kBAAkB;QAClB,eAAe,aAAa,eAAe;IAC7C;AACF;AAEA,eAAe,qBAAqB,MAAM;IACxC,MAAM,MAAM,IAAI,IAAI,6DAAwC,0BAA0B,EAAE,QAAQ;IAChG,MAAM,MAAM,MAAM,MAAM,IAAI,QAAQ,IAAI;QAAE,OAAO;IAAW;IAC5D,IAAI,CAAC,IAAI,EAAE,EAAE,MAAM,IAAI,MAAM,CAAC,WAAW,EAAE,IAAI,MAAM,EAAE;IACvD,OAAO,IAAI,IAAI;AACjB;AAEO,eAAe,iBAAiB,EAAE,MAAM,EAAE;IAC/C,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD;IACN,MAAM,aAAa,OAAO,MAAM;IAEhC,iEAAiE;IACjE,IAAI;QACF,MAAM,OAAO,MAAM,qBAAqB;QACxC,MAAM;QAEN,OAAO;YACL,OAAO,CAAC,QAAQ,EAAE,MAAM,MAAM,MAAM,uBAAuB,CAAC;YAC5D,aAAa,MAAM,MAAM;YACzB,WAAW;gBACT,OAAO,CAAC,QAAQ,EAAE,MAAM,MAAM,MAAM,uBAAuB,CAAC;gBAC5D,aAAa,MAAM,MAAM;gBACzB,QAAQ;oBAAC;wBACP,KAAK,MAAM,MAAM,qBAAqB;wBACtC,OAAO;wBACP,QAAQ;oBACV;iBAAE;YACJ;YACA,SAAS;gBACP,OAAO,CAAC,QAAQ,EAAE,MAAM,MAAM,MAAM,uBAAuB,CAAC;gBAC5D,aAAa,MAAM,MAAM;gBACzB,MAAM;gBACN,QAAQ;oBAAC,MAAM,MAAM,qBAAqB;iBAA0E;YACtH;YACA,OAAO;gBACL,MAAM;oBACJ;wBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;wBACnF,MAAM;oBACR;oBACA;wBACE,KAAK,CAAC,2BAA2B,EAAE,YAAY,mCAAmC,CAAC;wBACnF,MAAM;oBACR;iBACD;YACH;QACF;IACF,EAAE,OAAM;QACN,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;AACF;AAEe,eAAe,gBAAgB,EAAE,MAAM,EAAE;IACtD,CAAA,GAAA,6HAAA,CAAA,mBAAO,AAAD;IAEN,MAAM,aAAa,OAAO,MAAM;IAChC,MAAM,OAAO,MAAM,qBAAqB;IACxC,MAAM,cAAc,KAAK,IAAI;IAE7B,MAAM,kBAAkB,+BAA+B,YAAY;IACnE,MAAM,UAAU;QAAC;KAAgB,EAAE,0BAA0B;IAE7D,qBACE;;0BACE,8OAAC,oIAAA,CAAA,UAAY;gBAAC,SAAS;;;;;;0BACvB,8OAAC,4HAAA,CAAA,UAAU;0BACT,cAAA,8OAAC,8LAAA,CAAA,YAAS;8BACR,cAAA,8OAAC,mLAAA,CAAA,UAAgB;wBACf,YAAY;wBACZ,aAAa;wBACb,aAAa,KAAK,YAAY;wBAC9B,aAAa,KAAK,WAAW;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}]}