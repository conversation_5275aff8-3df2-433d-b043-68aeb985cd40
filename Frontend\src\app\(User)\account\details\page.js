'use client';
import { Row } from "react-bootstrap";
import React, { useState, useEffect } from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import MetaHead from "@/Seo/Meta/MetaHead";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import "@/css/account/AccountDetails.scss";
import PersonalInformation from "./Partial/PersonalInformation";
import LocalizationSettings from "./Partial/LocalizationSettings";
import PhoneNumber from "./Partial/PhoneNumber";
import Email from "./Partial/Email";
import Username from "./Partial/Username";
import AddressBook from "./Partial/AddressBook";
import { get } from "@/utils/apiUtils";
import { useSelector, useDispatch } from 'react-redux';
import { setUser } from '@/redux/authSlice';

export default function AcoountDetails() {
  const [userData, setUserData] = useState(null);
  const [addresses, setAddresses] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const reduxUser = useSelector((state) => state?.auth?.user || null);
  const dispatch = useDispatch();

  // Fetch all data in a single API call
  const fetchAllData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Make both API calls concurrently
      const [accountResponse, addressesResponse] = await Promise.all([
        get('/account'),
        get('/addresses')
      ]);

      // Handle account data
      if (accountResponse.success && accountResponse.data) {
        setUserData(accountResponse.data);
        // Update Redux store with fresh user data
        dispatch(setUser(accountResponse.data));
        // Also update localStorage to ensure consistency
        localStorage.setItem('user', JSON.stringify(accountResponse.data));
      } else {
        throw new Error(accountResponse.message || 'Failed to fetch user data');
      }

      // Handle addresses data
      if (addressesResponse.success && addressesResponse.data) {
        setAddresses(addressesResponse.data);
      } else {
        // Addresses might be empty, so don't throw error
        setAddresses([]);
      }

    } catch (err) {
      console.error('Error fetching account details data:', err);
      setError(err.message || 'Failed to load account details');

      // Fallback to Redux user data if API fails
      if (reduxUser) {
        setUserData(reduxUser);
      }
    } finally {
      setLoading(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchAllData();
  }, []);
  const metaArray = {
    noindex: true,
    title: "Account Details | Update Info | TradeReply",
    description: "Update your personal information on TradeReply.com. Manage your phone number, email, language, time zone, and other account settings.",
    canonical_link: "https://www.tradereply.com/account/details",
    og_site_name: "TradeReply",
    og_title: "Account Details | Update Info | TradeReply",
    og_description: "Update your personal information on TradeReply. Manage your phone number, email, language, time zone, and other account settings.",
    twitter_title: "Account Details | Update Info | TradeReply",
    twitter_description: "Update your personal information on TradeReply. Manage your phone number, email, language, time zone, and other account settings.",
  };

  // Show loading state
  if (loading) {
    return (
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_details">
          <SidebarHeading title="Account Details" />
          <div className="text-center py-5">
            <span>Loading account details...</span>
          </div>
        </div>
      </AccountLayout>
    );
  }

  // Show error state
  if (error && !userData) {
    return (
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_details">
          <SidebarHeading title="Account Details" />
          <div className="text-center py-5">
            <span className="text-danger">Failed to load account details</span>
            <br />
            <button
              className="btn btn-sm btn-link mt-2"
              onClick={fetchAllData}
              style={{ color: '#007bff', textDecoration: 'underline' }}
            >
              Retry
            </button>
          </div>
        </div>
      </AccountLayout>
    );
  }

  return (
    <>
      <AccountLayout>
        <MetaHead props={metaArray} />
        <div className="account_details">
          <SidebarHeading title="Account Details" />
          <Row>
            <PersonalInformation userData={userData} onDataUpdate={setUserData} />
            <LocalizationSettings userData={userData} onDataUpdate={setUserData} />
            <PhoneNumber userData={userData} onDataUpdate={setUserData} />
            <Email userData={userData} onDataUpdate={setUserData} />
            <Username userData={userData} onDataUpdate={setUserData} />
            <AddressBook addresses={addresses} onAddressesUpdate={setAddresses} />
          </Row>
        </div>
      </AccountLayout>
    </>
  )
}
