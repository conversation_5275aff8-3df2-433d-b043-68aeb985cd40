import CategoryClient from "./CategoryClient";
import { cookies } from "next/headers";
import JsonLdSchema, { generateCollectionPageSchema, generateBreadcrumbListSchema, generateCategoryBreadcrumbs } from "@/Seo/Schema/JsonLdSchema";

export default async function CategoryPage({ params, searchParams }) {
  const slug = searchParams?.slug || null;
  const page = parseInt(params?.id) || 1;
  const cookieStore = cookies();
  const key = cookieStore.get("categorySearchKey")?.value || "";

  const canonicalLink =
    page === 1
      ? `https://www.tradereply.com/category`
      : `https://www.tradereply.com/category/page/${page}`;

  let data = {
    allcategories: [],
    articles: [],
    meta: {},
    selected_category: null,
  };

  let categoryPagination = {};
  let nextLink = null;

  try {
    const query = new URLSearchParams({ slug, key, page }).toString();
    const apiBase = process.env.NEXT_PUBLIC_API_BASE_URL || '';
    const res = await fetch(`${apiBase}/category?${query}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!res.ok) {
      throw new Error("Failed to fetch category data");
    }

    const response = await res.json();
    data = response.data;
    console.log("API RESPONSE (server):", JSON.stringify(response?.data, null, 2));

    categoryPagination = data.meta;
    if (categoryPagination?.current_page < categoryPagination?.total) {
      nextLink = `https://www.tradereply.com/category/page/${categoryPagination.current_page + 1}`;
    }

  } catch (error) {
    console.error("Failed to fetch category data:", error);
  }

  const isSearch = key?.trim() !== "";
  console.log(data.articles, "test");
  const metaArray = {
    title: "TradeReply Categories | Explore Trading Content",
    description: "Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.",
    og_title: "TradeReply Categories | Explore Trading Content",
    og_description: "Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.",
    og_site_name: "TradeReply",
    twitter_title: "TradeReply Categories | Explore Trading Content",
    twitter_description: "Explore curated content on TradeReply.com. Browse blog articles and educational resources grouped by category to deepen your trading knowledge.",
    noindex: isSearch,
    ...(isSearch ? {} : { canonical_link: canonicalLink }),
    rel_next: nextLink,
  };
  const categorySchemas = !isSearch ? [
    generateCollectionPageSchema({
      name: "TradeReply Categories",
      description: "Explore curated trading content and educational resources on TradeReply.com",
      url: canonicalLink,
      articles: data.articles,
      currentPage: page
    }),
    generateBreadcrumbListSchema({
      items: generateCategoryBreadcrumbs("Categories", page > 1 ? page : null)
    })
  ] : [];
  // Generate JSON-LD schemas only when NOT in search mode
  // let categorySchemas = [];
  // if (!isSearch && data.articles && data.articles.length > 0) {
  //   // Generate CollectionPage schema
  //   const categoryName = data.selected_category?.title || "Latest Articles";
  //   const collectionPageSchema = generateCollectionPageSchema({
  //     name: categoryName,
  //     description: "Explore curated trading content and educational resources on TradeReply.com",
  //     url: canonicalLink,
  //     articles: data.articles,
  //     currentPage: page
  //   });

  //   // Generate BreadcrumbList schema
  //   const breadcrumbItems = generateCategoryBreadcrumbs(categoryName, page > 1 ? page : null);
  //   const breadcrumbSchema = generateBreadcrumbListSchema({
  //     items: breadcrumbItems
  //   });

  //   // Add schemas to array (only if they were generated successfully)
  //   if (collectionPageSchema) categorySchemas.push(collectionPageSchema);
  //   if (breadcrumbSchema) categorySchemas.push(breadcrumbSchema);
  // }
console.log("categorySchemas", categorySchemas);
  return (
    <>
       <JsonLdSchema schemas={categorySchemas} />
      <CategoryClient
        initialData={data}
        slug={slug}
        keyWord={key}
        currentPage={page}
        metaArray={metaArray}
      />
    </>
  );
}