{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/account/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/account/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/account/MarketPlace.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.container {\r\n  @media (width<=550px) {\r\n    padding: 0 !important;\r\n  }\r\n}\r\n\r\n.account {\r\n  &_search {\r\n    width: 100%;\r\n    height: 72px;\r\n    background-color: #ffffff33;\r\n    padding: 10px 20px;\r\n    border-radius: 15px;\r\n    display: flex;\r\n    gap: 16px;\r\n    margin-bottom: 26px;\r\n\r\n    input {\r\n      background-color: transparent;\r\n      width: 100%;\r\n      height: 100%;\r\n      color: #ffffff99;\r\n      font-size: 20px;\r\n      font-weight: 600;\r\n\r\n      &:focus {\r\n        box-shadow: none;\r\n        outline: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_card {\r\n    &_btnArrow {\r\n      min-width: 30px;\r\n      max-width: 30px;\r\n      min-height: 29px;\r\n      max-height: 29px;\r\n      border-radius: 50%;\r\n      display: flex;\r\n      justify-content: center;\r\n      align-items: center;\r\n      background-color: #00adef;\r\n\r\n      svg {\r\n        color: white !important;\r\n        margin-left: 0px !important;\r\n        transition: all ease-in-out 0.3s;\r\n\r\n        path {\r\n          fill: white !important;\r\n        }\r\n\r\n        @media (max-width: 767px) {\r\n          height: 10px !important;\r\n        }\r\n      }\r\n\r\n      &:hover {\r\n        background-color: #0099d1;\r\n      }\r\n    }\r\n\r\n    &_information {\r\n      color: var.$black !important;\r\n\r\n      .main_inform {\r\n        display: flex;\r\n        gap: 20px;\r\n      }\r\n\r\n      h4 {\r\n        color: var.$black !important;\r\n        font-size: 20px;\r\n        font-weight: 600;\r\n\r\n        @media (max-width: 991px) {\r\n          font-size: 15px;\r\n        }\r\n      }\r\n\r\n      .mail {\r\n        color: var.$black !important;\r\n        font-size: 16px;\r\n        font-weight: 400;\r\n        margin-bottom: 7px;\r\n        font-family: \"Gilroy-Semibold\", sans-serif;\r\n      }\r\n\r\n      .location,\r\n      .transaction,\r\n      .active_sign,\r\n      .since {\r\n        color: var.$black !important;\r\n        font-size: 14px;\r\n        font-family: \"Gilroy-Semibold\", sans-serif;\r\n        font-weight: 400;\r\n        margin-bottom: 6px;\r\n        display: flex;\r\n        gap: 9px;\r\n      }\r\n\r\n      .active_sign span {\r\n        height: 12px;\r\n        width: 12px;\r\n        border-radius: 50%;\r\n        background: #d9d9d9;\r\n        align-self: center;\r\n      }\r\n\r\n      .profile_photo {\r\n        background-color: #0000001a;\r\n        height: 100px;\r\n        width: 100px;\r\n        padding: 20px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      .profile_updateBtn {\r\n        font-size: 14px;\r\n        color: #00adef;\r\n        margin-top: 5px;\r\n        cursor: pointer;\r\n        font-weight: bold;\r\n      }\r\n\r\n      .round-bluefill-btn {\r\n        background-color: var.$baseclr;\r\n        color: white;\r\n        padding: 5px 15px;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        border-radius: 15px;\r\n        position: absolute;\r\n        bottom: 0;\r\n        width: 100%;\r\n        transition: 0.3s ease-in-out;\r\n\r\n        &:hover {\r\n          background-color: var.$baseclrhover;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_about {\r\n      color: var.$black !important;\r\n\r\n      .para_desc {\r\n        color: var.$black !important;\r\n        font-family: \"Gilroy-Semibold\", sans-serif;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        margin-bottom: 7px;\r\n        line-height: 26px;\r\n      }\r\n\r\n      .website-link {\r\n        color: var.$baseclr;\r\n        font-size: 14px;\r\n        font-weight: 700;\r\n        word-break: break-all;\r\n\r\n        &:hover {\r\n          color: #fea500;\r\n        }\r\n      }\r\n\r\n      .table_form_textarea {\r\n        background-color: #4f5e7a;\r\n        border-radius: 6px;\r\n        color: white;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        width: 100%;\r\n        padding: 5px 8px;\r\n\r\n        &:focus {\r\n          outline: none;\r\n          background-color: #576887;\r\n        }\r\n      }\r\n\r\n      .character-count {\r\n        text-align: end;\r\n        margin-top: 2px;\r\n        color: #4f5e7a;\r\n        font-size: 14px;\r\n      }\r\n    }\r\n\r\n    &_followers,\r\n    &_following {\r\n      color: var.$black !important;\r\n\r\n      .main_inform {\r\n        display: flex;\r\n        gap: 12px;\r\n        align-items: center;\r\n        margin-bottom: 6px;\r\n      }\r\n\r\n      h6 {\r\n        color: var.$baseclr;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .profile_photo {\r\n        background-color: #0000001a;\r\n        height: 50px;\r\n        width: 50px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        flex-shrink: 0 !important;\r\n\r\n        img {\r\n          height: 20px !important;\r\n          width: 20px !important;\r\n        }\r\n      }\r\n\r\n      .unFollow_status {\r\n        background-color: #ff696a;\r\n        height: 43px;\r\n        padding: 10px 15px;\r\n        display: flex;\r\n        align-items: center;\r\n        border-radius: 50px;\r\n        flex-shrink: 0;\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        cursor: pointer;\r\n\r\n        &:hover {\r\n          background-color: #fe5252;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_marketplace {\r\n      color: var.$black !important;\r\n\r\n      .mini_card {\r\n        border: 1px solid #06060666;\r\n        padding: 10px;\r\n        border-radius: 15px;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .main_inform {\r\n        display: flex;\r\n        gap: 10px;\r\n        align-items: center;\r\n        margin-bottom: 6px;\r\n      }\r\n\r\n      .star-rating {\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .most_recent,\r\n      .time {\r\n        color: var.$black !important;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .profile_photo {\r\n        background-color: #0000001a;\r\n        height: 50px;\r\n        width: 50px;\r\n        padding: 15px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 50%;\r\n        flex-shrink: 0;\r\n      }\r\n\r\n      h6 {\r\n        color: var.$black !important;\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        font-family: \"Gilroy-Semibold\", sans-serif;\r\n      }\r\n\r\n      .mini_sc_title {\r\n        display: none;\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        h6 {\r\n          display: none;\r\n        }\r\n\r\n        .mini_sc_title {\r\n          display: block;\r\n        }\r\n      }\r\n\r\n      .small_tag {\r\n        color: var.$baseclr !important;\r\n        font-size: 15px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .time {\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .thumbs_text {\r\n        color: var.$black !important;\r\n        font-size: 14px;\r\n        font-weight: 400;\r\n        font-family: \"Gilroy-Semibold\", sans-serif;\r\n      }\r\n    }\r\n\r\n    &_active_listing {\r\n      color: var.$black !important;\r\n\r\n      .mini_card {\r\n        border: 1px solid #06060666;\r\n        padding: 10px;\r\n        border-radius: 15px;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .main_inform {\r\n        display: flex;\r\n        gap: 10px;\r\n        align-items: center;\r\n        margin-bottom: 6px;\r\n      }\r\n\r\n      .star-rating {\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .most_recent {\r\n        color: var.$black !important;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .respon_sell_feedback {\r\n        align-items: start;\r\n        gap: 17px;\r\n\r\n        @media (max-width: 900px) {\r\n          display: block;\r\n\r\n          h6 {\r\n            margin-top: 10px;\r\n          }\r\n\r\n          .activeListing_photo {\r\n            max-width: 100%;\r\n            height: 150px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .activeListing_photo {\r\n        width: 100%;\r\n        aspect-ratio: 1 / 1;\r\n        max-width: 158px;\r\n        border-radius: 5px;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: cover;\r\n        }\r\n      }\r\n\r\n      h6 {\r\n        color: var.$black !important;\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        font-family: \"Gilroy-Semibold\", sans-serif;\r\n      }\r\n\r\n      .inner_price_text {\r\n        color: var.$black !important;\r\n        font-size: 15px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .round-border-btn,\r\n      .rounded-border-btn {\r\n        border: 1px solid #00000033;\r\n        background-color: #0000000d;\r\n        padding: 10px 10px 10px 10px;\r\n        gap: 5px;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        border-radius: 50px;\r\n        display: flex;\r\n        transition: all 0.4s ease-in-out;\r\n\r\n        &:hover {\r\n          border: 1px solid rgba(0, 0, 0, 0.477);\r\n        }\r\n      }\r\n\r\n      .rounded-border-btn {\r\n        padding: 10px 18px 10px 18px !important;\r\n      }\r\n\r\n      .round-bluefill-btn {\r\n        background-color: var.$baseclr;\r\n        color: white;\r\n        padding: 5px;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        border-radius: 50px;\r\n        transition: 0.3s ease-in-out;\r\n\r\n        &:hover {\r\n          background-color: var.$baseclrhover;\r\n        }\r\n      }\r\n\r\n      .dropdownlist {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 12px;\r\n        padding-top: 7px;\r\n        padding-bottom: 7px;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n\r\n        img {\r\n          width: 18px !important;\r\n          height: 18px !important;\r\n          min-width: 18px !important;\r\n        }\r\n\r\n        span {\r\n          text-align: left;\r\n          flex: 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_dash_listings {\r\n      color: var.$black !important;\r\n\r\n      .mini_card {\r\n        border: 1px solid #06060666;\r\n        padding: 10px;\r\n        border-radius: 15px;\r\n        margin-bottom: 10px;\r\n      }\r\n\r\n      .main_inform {\r\n        display: flex;\r\n        gap: 10px;\r\n        align-items: center;\r\n        margin-bottom: 6px;\r\n      }\r\n\r\n      .star-rating {\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .most_recent {\r\n        color: var.$black !important;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .respon_sell_feedback {\r\n        align-items: start;\r\n        gap: 17px;\r\n\r\n        @media (max-width: 767px) {\r\n          display: block;\r\n\r\n          h6 {\r\n            margin-top: 10px;\r\n          }\r\n\r\n          .activeListing_photo {\r\n            max-width: 100%;\r\n            height: 150px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .activeListing_photo {\r\n        width: 100%;\r\n        aspect-ratio: 1 / 1;\r\n        max-width: 158px;\r\n        border-radius: 5px;\r\n        overflow: hidden;\r\n        flex-shrink: 0;\r\n\r\n        img {\r\n          width: 100%;\r\n          height: 100%;\r\n          object-fit: cover;\r\n        }\r\n      }\r\n\r\n      h6 {\r\n        color: var.$black !important;\r\n        font-size: 18px;\r\n        font-weight: 400;\r\n        font-family: \"Gilroy-Semibold\", sans-serif;\r\n      }\r\n\r\n      .inner_price_text {\r\n        color: var.$black !important;\r\n        font-size: 15px;\r\n        font-weight: 600;\r\n      }\r\n\r\n      .actions_btn {\r\n        width: 100%;\r\n        justify-content: space-between;\r\n        display: flex;\r\n        margin-top: 50px;\r\n        flex-direction: row-reverse;\r\n        flex-wrap: wrap;\r\n\r\n        @media (max-width: 576px) {\r\n          margin-top: 12px;\r\n        }\r\n\r\n        .first_part {\r\n          position: relative;\r\n          display: flex;\r\n          gap: 8px;\r\n          margin-bottom: 12px;\r\n\r\n          @media (max-width: 576px) {\r\n            justify-content: end;\r\n          }\r\n        }\r\n\r\n        .second_part {\r\n          display: flex;\r\n          gap: 8px;\r\n\r\n          @media (max-width: 576px) {\r\n            justify-content: end;\r\n            width: 100%;\r\n\r\n            button {\r\n              width: 50%;\r\n            }\r\n          }\r\n        }\r\n\r\n        .round-border-btn,\r\n        .rounded-border-btn {\r\n          border: 1px solid #00000033;\r\n          background-color: #0000000d;\r\n          padding: 6px 12px 6px 12px;\r\n          gap: 5px;\r\n          font-size: 14px;\r\n          font-weight: 600;\r\n          transition: all 0.4s ease-in-out;\r\n          border-radius: 50px;\r\n          width: auto;\r\n          height: 35px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: center;\r\n          display: inline-flex;\r\n\r\n          &:hover {\r\n            border: 1px solid rgba(0, 0, 0, 0.477);\r\n          }\r\n        }\r\n\r\n        .rounded-border-btn {\r\n          padding: 10px 15px !important;\r\n        }\r\n\r\n        .round-bluefill-btn {\r\n          background-color: var.$baseclr;\r\n          color: white;\r\n          padding: 6px 18px;\r\n          font-size: 14px;\r\n          font-weight: 600;\r\n          border-radius: 50px;\r\n          transition: 0.3s ease-in-out;\r\n          height: 35px;\r\n          width: fit-content;\r\n          align-items: center;\r\n          justify-content: center;\r\n          display: inline-flex;\r\n          white-space: nowrap;\r\n\r\n          &:hover {\r\n            background-color: var.$baseclrhover;\r\n          }\r\n        }\r\n\r\n        .dropdownlist {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 14px;\r\n          padding-top: 7px;\r\n          padding-bottom: 7px;\r\n          color: black;\r\n          font-size: 14px;\r\n          font-weight: 600;\r\n\r\n          img {\r\n            width: 18px !important;\r\n            height: 18px !important;\r\n            min-width: 18px !important;\r\n          }\r\n\r\n          span {\r\n            text-align: left;\r\n            flex: 1;\r\n          }\r\n\r\n          &:hover {\r\n            color: #00adef;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &_insight {\r\n      color: var.$black !important;\r\n\r\n      .row {\r\n        border-bottom: 1px solid #00000033;\r\n      }\r\n\r\n      .row:last-child {\r\n        border-bottom: none;\r\n      }\r\n\r\n      .wrap_div {\r\n        display: flex;\r\n        gap: 7px;\r\n        align-items: start;\r\n        padding: 10px 2px;\r\n\r\n        @media (max-width: 323) {\r\n          gap: 5px;\r\n        }\r\n\r\n        img {\r\n          margin-top: 2.3px;\r\n          flex-shrink: 0;\r\n        }\r\n      }\r\n\r\n      p {\r\n        color: var.$black;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        display: flex;\r\n        gap: 10px;\r\n        align-items: start;\r\n      }\r\n    }\r\n\r\n    &_disputes {\r\n      color: var.$black !important;\r\n\r\n      .lg_screen_table {\r\n        table {\r\n          border-collapse: separate;\r\n          width: 100%;\r\n          table-layout: fixed;\r\n          margin-bottom: 0px;\r\n\r\n          tr:last-child {\r\n            border-bottom: 0px solid white;\r\n          }\r\n\r\n          th {\r\n            color: white;\r\n            vertical-align: middle;\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            background-color: var.$clr031940;\r\n            border-radius: 15px;\r\n            padding: 10px;\r\n            border-right: 4px solid white;\r\n            border-left: 1.5px solid white;\r\n            margin: 0px 10px;\r\n\r\n            .th-inner {\r\n              display: flex;\r\n              gap: 8px;\r\n            }\r\n          }\r\n\r\n          td {\r\n            color: #000000;\r\n            padding: 10px;\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            border-right: 1px solid #00000033;\r\n            margin-bottom: 5px;\r\n            word-wrap: break-word;\r\n          }\r\n\r\n          td:last-child {\r\n            border-right: none;\r\n          }\r\n\r\n          .view_res_btn {\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            min-width: 100% !important;\r\n            width: 100% !important;\r\n            min-height: auto !important;\r\n            height: auto !important;\r\n            border-radius: 20px !important;\r\n            padding: 8px 11px !important;\r\n          }\r\n        }\r\n\r\n        .bullet-points {\r\n          display: flex;\r\n          align-items: start;\r\n          gap: 8px;\r\n          margin-bottom: 8px;\r\n\r\n          img {\r\n            flex-shrink: 0;\r\n            margin-top: 7px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .sm_screen_table {\r\n        .wrap-div {\r\n          border-bottom: 1px solid #00000066;\r\n          margin-bottom: 10px;\r\n        }\r\n\r\n        .wrap-div:last-child {\r\n          border-bottom: none;\r\n        }\r\n\r\n        .row {\r\n          .colunm_head {\r\n            color: white;\r\n            background-color: #031940;\r\n            padding: 10px;\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            margin-bottom: 5px;\r\n            display: flex;\r\n            gap: 4px;\r\n            border-radius: 15px;\r\n          }\r\n\r\n          .colunm_value {\r\n            color: black;\r\n            font-size: 14px;\r\n            font-weight: 600;\r\n            border-right: 1px solid #00000033;\r\n            margin-top: 3px;\r\n            margin-bottom: 3px;\r\n            padding: 0 7px;\r\n            word-wrap: break-word;\r\n          }\r\n\r\n          .colunm_value:last-child,\r\n          .colunm_value:nth-child(4) {\r\n            border: none;\r\n          }\r\n\r\n          .arrow-header {\r\n            width: 30px;\r\n          }\r\n        }\r\n\r\n        .bullet-points {\r\n          display: flex;\r\n          align-items: start;\r\n          gap: 8px;\r\n          margin-bottom: 8px;\r\n\r\n          img {\r\n            flex-shrink: 0;\r\n            margin-top: 7px;\r\n          }\r\n        }\r\n      }\r\n\r\n      .no_disputes {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        color: black;\r\n      }\r\n\r\n      .table_form_textarea {\r\n        background-color: #4f5e7a;\r\n        border-radius: 6px;\r\n        color: white;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        width: 100%;\r\n        padding: 5px 8px;\r\n\r\n        &:focus {\r\n          outline: none;\r\n          background-color: #576887;\r\n        }\r\n      }\r\n\r\n      .character-count {\r\n        text-align: end;\r\n        margin-top: 2px;\r\n        color: #4f5e7a;\r\n        font-size: 14px;\r\n      }\r\n\r\n      .file-upload-wrapper {\r\n        display: inline-block;\r\n        width: 100%;\r\n        border: 2px solid #ddd;\r\n        border-radius: 9999px;\r\n        padding: 6px 20px;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        background-color: white;\r\n        color: black;\r\n        cursor: pointer;\r\n        transition: background-color 0.2s ease;\r\n        height: 37px;\r\n        text-align: center;\r\n      }\r\n\r\n      .file-upload-wrapper:hover {\r\n        background-color: #f0f0f0;\r\n      }\r\n\r\n      .file-upload-wrapper input[type=\"file\"] {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    &_timeline {\r\n      color: var.$black !important;\r\n\r\n      h2 {\r\n        color: white;\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        padding: 0;\r\n        background-color: var.$clr031940;\r\n        border-radius: 15px;\r\n        padding: 12px;\r\n        display: inline-flex;\r\n      }\r\n\r\n      ul {\r\n        padding-left: 17px;\r\n        margin: 13px 0px;\r\n\r\n        li {\r\n          color: black;\r\n          font-size: 14px;\r\n          font-weight: 600;\r\n          border-radius: 15px;\r\n          list-style-type: circle;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_create_listing {\r\n      .form-wrapper {\r\n\r\n        .form-input,\r\n        .form-select,\r\n        .form-textarea {\r\n          padding: 1rem;\r\n          border-radius: 15px;\r\n          border: none;\r\n          width: 100%;\r\n          color: black;\r\n          font-size: 18px;\r\n          margin-bottom: 12px;\r\n\r\n          @media (max-width: 550px) {\r\n            font-size: 14px;\r\n          }\r\n\r\n          &::placeholder {\r\n            color: #000000cc;\r\n          }\r\n\r\n          &:focus {\r\n            outline: none;\r\n          }\r\n        }\r\n\r\n        .tags-input {\r\n          background: white;\r\n          display: flex;\r\n          flex-wrap: wrap;\r\n          padding: 0.8rem 1rem;\r\n          border-radius: 15px;\r\n          margin-bottom: 12px;\r\n          gap: 10px;\r\n\r\n          input {\r\n            color: black;\r\n            width: 100%;\r\n            font-size: 18px;\r\n            border: none;\r\n\r\n            @media (max-width: 501px) {\r\n              font-size: 15px;\r\n            }\r\n\r\n            &::placeholder {\r\n              color: #000000cc;\r\n            }\r\n\r\n            &:focus {\r\n              outline: none;\r\n            }\r\n          }\r\n\r\n          .tag {\r\n            background-color: #e6e6e6;\r\n            padding: 5px 9px !important;\r\n            color: black;\r\n            border-radius: 50px !important;\r\n            gap: 12px !important;\r\n            display: flex;\r\n            align-items: center;\r\n            font-size: 18px;\r\n\r\n            svg {\r\n              cursor: pointer;\r\n              font-weight: 700;\r\n            }\r\n          }\r\n        }\r\n\r\n        .checkbox-wrapper {\r\n          display: flex;\r\n          align-items: center;\r\n          gap: 0.6rem;\r\n          margin-bottom: 11px;\r\n\r\n          .custom_checkbox_input {\r\n            height: 20px;\r\n            width: 20px;\r\n          }\r\n\r\n          .custom_checkbox_input:checked {\r\n            background-color: #00adef !important;\r\n            border: 1px solid #00adef !important;\r\n          }\r\n        }\r\n\r\n        .file-upload-wrapper {\r\n          display: flex;\r\n          width: 100%;\r\n          height: 46px;\r\n          align-items: center;\r\n          justify-content: center;\r\n          border: 2px solid #ddd;\r\n          border-radius: 9999px;\r\n          padding: 6px 20px;\r\n          margin-bottom: 12px;\r\n          font-size: 16px;\r\n          font-weight: 600;\r\n          background-color: white;\r\n          color: black;\r\n          cursor: pointer;\r\n          transition: background-color 0.2s ease;\r\n\r\n          @media (max-width: 501px) {\r\n            font-size: 15px;\r\n          }\r\n        }\r\n\r\n        .file-upload-wrapper:hover {\r\n          background-color: #f0f0f0;\r\n        }\r\n\r\n        .file-upload-wrapper input[type=\"file\"] {\r\n          display: none;\r\n        }\r\n\r\n        .character-count {\r\n          font-size: 0.9rem;\r\n          font-weight: 600;\r\n          color: #000000cc;\r\n          position: absolute;\r\n          bottom: 18;\r\n          right: 11;\r\n        }\r\n\r\n        .outer-character-count {\r\n          text-align: end;\r\n          margin-bottom: 12px;\r\n          font-size: 0.9rem;\r\n          font-weight: 600;\r\n          color: #ffffffcc;\r\n        }\r\n\r\n        .upload-container {\r\n          height: 100%;\r\n\r\n          @media (width<=991px) {\r\n            margin-bottom: 20px;\r\n          }\r\n\r\n          .upload-box {\r\n            background: #fff;\r\n            border-radius: 12px;\r\n            padding: 0.5rem;\r\n            text-align: center;\r\n            cursor: pointer;\r\n            height: 87%;\r\n            display: flex;\r\n            align-items: center;\r\n            justify-content: center;\r\n            width: 100%;\r\n            font-size: 20px;\r\n            font-weight: 600;\r\n\r\n\r\n            @media (767px<=width<=991px) {\r\n              height: 300px;\r\n            }\r\n\r\n            @media (width<=768px) {\r\n              height: 200px;\r\n            }\r\n\r\n            .upload-placeholder {\r\n              img {\r\n                margin: 0 auto;\r\n              }\r\n\r\n              p {\r\n                font-size: 20px;\r\n                font-weight: 600;\r\n                margin: 0.35rem 0 0 0;\r\n                color: black;\r\n              }\r\n\r\n              .sub-text {\r\n                font-size: 15px !important;\r\n                margin: 0;\r\n              }\r\n            }\r\n\r\n            .preview {\r\n              position: relative;\r\n              width: 100%;\r\n              height: 100%;\r\n              overflow-y: hidden;\r\n              display: flex;\r\n              justify-content: center;\r\n              align-items: center;\r\n\r\n              img {\r\n                width: 100%;\r\n                height: 80%;\r\n                object-fit: contain;\r\n                border-radius: 8px;\r\n              }\r\n\r\n              .remove-btn {\r\n                position: absolute;\r\n                top: 55%;\r\n                right: 46%;\r\n                transform: translate(-50%, -50%);\r\n                background: #ff4d4f;\r\n                border: none;\r\n                color: white;\r\n                font-weight: bold;\r\n                border-radius: 50%;\r\n                width: 25px;\r\n                height: 25px;\r\n                cursor: pointer;\r\n                font-size: 14px;\r\n              }\r\n            }\r\n          }\r\n\r\n          .image-count {\r\n            margin: 0.5rem 0;\r\n            font-size: 15px;\r\n            font-weight: 600;\r\n            color: white;\r\n          }\r\n        }\r\n\r\n        .form-multi-select {\r\n          color: black;\r\n          font-size: 18px;\r\n          border-radius: 15px;\r\n        }\r\n\r\n        // .form-select-badge {\r\n        //   font-size: 18px;\r\n        //   margin-top: 5px;\r\n        //   margin-bottom: 5px;\r\n        //   border-radius: 15px;\r\n        //   color: black;\r\n        // }\r\n        .select__control {\r\n          padding: 9px;\r\n          border-radius: 15px;\r\n          font-size: 18px;\r\n        }\r\n\r\n        .select__placeholder {\r\n          color: black !important;\r\n        }\r\n\r\n        .select__multi-value {\r\n          border-radius: 20px;\r\n          padding: 2px 6px;\r\n          font-size: 18px;\r\n        }\r\n\r\n        .select__multi-value__label {\r\n          font-size: 18px;\r\n          border-radius: 5px;\r\n        }\r\n\r\n        .select__multi-value__remove {\r\n          color: #000;\r\n          border-radius: 50%;\r\n          padding-left: 9px;\r\n          padding-right: 9px;\r\n        }\r\n\r\n        .select__multi-value__remove:hover {\r\n          background-color: lightgray;\r\n          border-radius: 50%;\r\n          padding-left: 9px;\r\n          padding-right: 9px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.listing-rightside {\r\n  @media (width>991px) {\r\n    margin-top: 36px;\r\n  }\r\n}\r\n\r\n.create-listing-categories {\r\n  position: relative;\r\n\r\n  .field {\r\n    cursor: pointer;\r\n    background-color: #fff;\r\n    padding: 1rem;\r\n    border-radius: 15px;\r\n    border: none;\r\n    width: 100%;\r\n    color: #000;\r\n    font-size: 18px;\r\n    margin-bottom: 12px;\r\n    display: flex;\r\n\r\n    @media (max-width: 550px) {\r\n      font-size: 14px;\r\n    }\r\n  }\r\n\r\n  .category-toggle {\r\n    width: 100%;\r\n  }\r\n\r\n  .dropdown {\r\n    position: absolute;\r\n    background-color: #fff;\r\n    border-radius: 15px;\r\n    border: none;\r\n    width: 100%;\r\n    color: #000;\r\n    font-size: 18px;\r\n    left: 0;\r\n    top: 65px;\r\n    z-index: 999;\r\n    box-shadow: rgba(50, 50, 93, 0.25) 0px 13px 27px -5px, rgba(0, 0, 0, 0.3) 0px 8px 16px -8px;\r\n\r\n    .dropdown-header {\r\n      padding: 0.5rem;\r\n      display: flex;\r\n      align-items: center;\r\n      border-bottom: 1px solid #e5e7eb;\r\n      cursor: pointer;\r\n\r\n      .dropdown-title {\r\n        width: 100%;\r\n        text-align: center;\r\n      }\r\n    }\r\n\r\n    ul {\r\n      li {\r\n        padding: 1rem;\r\n        cursor: pointer;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n\r\n        @media (max-width: 550px) {\r\n          padding: 0.75rem;\r\n          font-size: 14px;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: #e6e6e6 !important;\r\n\r\n          &:first-child {\r\n            border-radius: 15px 15px 0 0;\r\n          }\r\n\r\n          &:last-child {\r\n            border-radius: 0 0 15px 15px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .sub_list {\r\n      li {\r\n        &:hover {\r\n          &:first-child {\r\n            border-radius: 0 !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .selected-values-container {\r\n    .selected-values {\r\n      width: fit-content;\r\n      padding: 5px 15px;\r\n      background-color: #00adef;\r\n      border-radius: 15px;\r\n      color: #fff;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      width: 100%;\r\n      gap: 10px;\r\n      margin-bottom: 12px;\r\n\r\n      .values {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 8px;\r\n\r\n        @media (max-width: 550px) {\r\n          display: block;\r\n        }\r\n\r\n        span {\r\n          gap: 5px;\r\n          font-size: 14px;\r\n          font-weight: 700;\r\n          display: flex;\r\n          align-items: center;\r\n        }\r\n      }\r\n\r\n      .remove-selected {\r\n        svg {\r\n          cursor: pointer;\r\n          width: 1.3rem;\r\n          height: 1.3rem;\r\n        }\r\n      }\r\n    }\r\n\r\n    .clear-all {\r\n      background-color: #ff696a;\r\n      color: #fff;\r\n      border-radius: 15px;\r\n      margin-bottom: 12px;\r\n      padding: 5px 10px;\r\n      font-size: 14px;\r\n      font-weight: 700;\r\n\r\n      &:hover {\r\n        background-color: #e65f60;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.disabled {\r\n  pointer-events: none;\r\n  opacity: 0.5;\r\n  cursor: not-allowed;\r\n}"], "names": [], "mappings": "AAGA;;;;AAAE;EADF;;;;;AAOE;;;;;;;;;;;AAUE;;;;;;;;;AAQE;;;;;AAQF;;;;;;;;;;;;AAWE;;;;;;AAKE;;;;AAIA;EATF;;;;;AAcA;;;;AAKF;;;;AAGE;;;;;AAKA;;;;;;AAKE;EALF;;;;;AAUA;;;;;;;;AAQA;;;;;;;;;;AAaA;;;;;;;;AAQA;;;;;;;;;;;;AAYA;;;;;;;;AAQA;;;;;;;;;;;;;AAYE;;;;AAMJ;;;;AAGE;;;;;;;;;AASA;;;;;;;AAME;;;;AAKF;;;;;;;;;;AASE;;;;;AAMF;;;;;;;AAQF;;;;AAIE;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;AAUE;;;;;AAMF;;;;;;;;;;;;;AAYE;;;;AAMJ;;;;AAGE;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAOA;;;;;;;;;;;;AAYA;;;;;;;AAOA;;;;AAIA;EACE;;;;EAIA;;;;;AAKF;;;;;;AAMA;;;;AAIA;;;;;;;AAQF;;;;AAGE;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;AAIE;EAJF;;;;EAOI;;;;EAIA;;;;;;AAOJ;;;;;;;;;AAQE;;;;;;AAOF;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;AAYE;;;;AAKF;;;;AAIA;;;;;;;;;;AASE;;;;AAKF;;;;;;;;;;AASE;;;;;;AAMA;;;;;AAOJ;;;;AAGE;;;;;;;AAOA;;;;;;;AAOA;;;;;AAKA;;;;;;AAMA;;;;;AAIE;EAJF;;;;EAOI;;;;EAIA;;;;;;AAOJ;;;;;;;;;AAQE;;;;;;AAOF;;;;;;;AAOA;;;;;;AAMA;;;;;;;;AAQE;EARF;;;;;AAYE;;;;;;;AAME;EANF;;;;;AAWA;;;;;AAIE;EAJF;;;;;EAQI;;;;;AAMJ;;;;;;;;;;;;;;;;AAiBE;;;;AAKF;;;;AAIA;;;;;;;;;;;;;;;;;AAeE;;;;AAKF;;;;;;;;;;;AAUE;;;;;;AAMA;;;;;AAKA;;;;AAON;;;;AAGE;;;;AAIA;;;;AAIA;;;;;;;AAME;EANF;;;;;AAUE;;;;;AAMF;;;;;;;;;AAUF;;;;AAII;;;;;;;AAME;;;;AAIA;;;;;;;;;;;;;AAYE;;;;;AAMF;;;;;;;;;;AAUA;;;;AAIA;;;;;;;;;;;AAYF;;;;;;;AAME;;;;;AAQF;;;;;AAKA;;;;AAKE;;;;;;;;;;;;AAYA;;;;;;;;;;;AAWA;;;;AAKA;;;;AAKF;;;;;;;AAME;;;;;AAOJ;;;;;;AAMA;;;;;;;;;;AASE;;;;;AAMF;;;;;;;AAOA;;;;;;;;;;;;;;;;AAgBA;;;;AAIA;;;;AAKF;;;;AAGE;;;;;;;;;;AAWA;;;;;AAIE;;;;;;;;AAaA;;;;;;;;;;AAWE;EAXF;;;;;AAeE;;;;AAIA;;;;AAKF;;;;;;;;;;AASE;;;;;;;AAME;EANF;;;;;AAUE;;;;AAIA;;;;AAKF;;;;;;;;;;;AAUE;;;;;AAOJ;;;;;;;AAME;;;;;AAKA;;;;;AAMF;;;;;;;;;;;;;;;;;;AAiBE;EAjBF;;;;;AAsBA;;;;AAIA;;;;AAIA;;;;;;;;;AASA;;;;;;;;AAQA;;;;AAGE;EAHF;;;;;AAOE;;;;;;;;;;;;;;;AAeE;EAfF;;;;;AAmBE;EAnBF;;;;;AAwBI;;;;AAIA;;;;;;;AAOA;;;;;AAMF;;;;;;;;;;AASE;;;;;;;AAOA;;;;;;;;;;;;;;;;AAkBJ;;;;;;;AAQF;;;;;;AAaA;;;;;;AAMA;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;;;;AAOA;;;;;;;AAYN;EADF;;;;;AAMA;;;;AAGE;;;;;;;;;;;;;AAYE;EAZF;;;;;AAiBA;;;;AAIA;;;;;;;;;;;;;;AAaE;;;;;;;;AAOE;;;;;AAOA;;;;;;;;AAOE;EAPF;;;;;;AAYE;;;;AAGE;;;;AAIA;;;;AAUA;;;;AASN;;;;;;;;;;;;;AAaE;;;;;;AAKE;EALF;;;;;AASE;;;;;;;;AAUA;;;;;;AAQJ;;;;;;;;;;AASE;;;;AAON"}}]}