{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/Home/BlogDetail.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.blog_detail {\r\n  &_tag {\r\n    padding: 6px 20px;\r\n    background-color: var.$baseclr;\r\n    border-radius: 10px;\r\n    text-align: center;\r\n    font-size: 16px;\r\n    font-weight: 600;\r\n    line-height: 26px;\r\n    letter-spacing: -0.10000000149011612px;\r\n    text-transform: uppercase;\r\n    color: var.$white;\r\n    border: 0;\r\n  }\r\n\r\n  &_heading {\r\n    h1 {\r\n      font-size: 2.8rem;\r\n      font-weight: 600;\r\n      color: var.$white;\r\n      padding: 30px 0;\r\n\r\n      @media (max-width: 1199px) {\r\n        font-size: 1.5rem;\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        font-size: 1.5rem;\r\n        line-height: 35px;\r\n      }\r\n    }\r\n\r\n    h5 {\r\n      font-size: 1.25rem;\r\n      font-weight: 600;\r\n      color: var.$white;\r\n      padding-top: 30px;\r\n\r\n      @media (max-width: 767px) {\r\n        font-size: 1.125rem;\r\n        line-height: 30px;\r\n        padding-top: 5px;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_postimg {\r\n    padding: 5rem 0;\r\n\r\n    @media (max-width: 767px) {\r\n      padding: 2rem 0;\r\n    }\r\n\r\n    img {\r\n      border-radius: 60px;\r\n      width: 100%;\r\n\r\n      @media (max-width: 767px) {\r\n        border-radius: 30px;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_text {\r\n    p {\r\n      font-size: 1.5rem;\r\n      font-weight: 400;\r\n      line-height: 36px;\r\n      letter-spacing: -0.1px;\r\n      color: var.$white;\r\n      padding-top: 20px;\r\n      max-width: 1000px;\r\n      white-space: normal;\r\n      word-wrap: break-word;\r\n      overflow: visible;\r\n      text-overflow: clip;\r\n\r\n      @media (max-width: 767px) {\r\n        font-size: 1rem;\r\n        line-height: 24px;\r\n        padding-top: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_author {\r\n    padding-top: 5rem;\r\n\r\n    @media (max-width: 767px) {\r\n      padding-top: 3rem;\r\n    }\r\n\r\n    &_btn {\r\n      background-color: transparent;\r\n      border: 0;\r\n      color: var.$baseclr;\r\n      font-size: 1.25rem;\r\n      font-weight: 600;\r\n      line-height: 24.5px;\r\n      letter-spacing: -0.10000000149011612px;\r\n      margin-bottom: 60px;\r\n\r\n      @media (max-width: 767px) {\r\n        font-size: 1rem;\r\n        line-height: 1.25rem;\r\n        margin-bottom: 30px;\r\n      }\r\n    }\r\n  }\r\n\r\n  .recent_post {\r\n    background-color: transparent;\r\n    border-radius: 0;\r\n    border: 0;\r\n    margin-bottom: 0;\r\n    padding: 30px 0;\r\n    border-top: 1px solid var.$borderclr;\r\n    border-bottom: 1px solid var.$borderclr;\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AAAE;;;;;;;;;;;;;;AAeE;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;;AAgBA;;;;;;;AAME;EANF;;;;;;;AAcF;;;;AAGE;EAHF;;;;;AAOE;;;;;AAIE;EAJF;;;;;AAWA;;;;;;;;;;;;;;AAaE;EAbF;;;;;;;AAqBF;;;;AAGE;EAHF;;;;;AAOE;;;;;;;;;;;AAUE;EAVF;;;;;;;AAkBF"}}]}