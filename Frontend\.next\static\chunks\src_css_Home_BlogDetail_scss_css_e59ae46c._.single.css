/* [project]/src/css/Home/BlogDetail.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.blog_detail_tag {
  text-align: center;
  letter-spacing: -.1px;
  text-transform: uppercase;
  color: #fff;
  background-color: #00adef;
  border: 0;
  border-radius: 10px;
  padding: 6px 20px;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
}

.blog_detail_heading h1 {
  color: #fff;
  padding: 30px 0;
  font-size: 2.8rem;
  font-weight: 600;
}

@media (max-width: 1199px) {
  .blog_detail_heading h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 767px) {
  .blog_detail_heading h1 {
    font-size: 1.5rem;
    line-height: 35px;
  }
}

.blog_detail_heading h5 {
  color: #fff;
  padding-top: 30px;
  font-size: 1.25rem;
  font-weight: 600;
}

@media (max-width: 767px) {
  .blog_detail_heading h5 {
    padding-top: 5px;
    font-size: 1.125rem;
    line-height: 30px;
  }
}

.blog_detail_postimg {
  padding: 5rem 0;
}

@media (max-width: 767px) {
  .blog_detail_postimg {
    padding: 2rem 0;
  }
}

.blog_detail_postimg img {
  border-radius: 60px;
  width: 100%;
}

@media (max-width: 767px) {
  .blog_detail_postimg img {
    border-radius: 30px;
  }
}

.blog_detail_text p {
  letter-spacing: -.1px;
  color: #fff;
  white-space: normal;
  word-wrap: break-word;
  text-overflow: clip;
  max-width: 1000px;
  padding-top: 20px;
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 36px;
  overflow: visible;
}

@media (max-width: 767px) {
  .blog_detail_text p {
    padding-top: 0;
    font-size: 1rem;
    line-height: 24px;
  }
}

.blog_detail_author {
  padding-top: 5rem;
}

@media (max-width: 767px) {
  .blog_detail_author {
    padding-top: 3rem;
  }
}

.blog_detail_author_btn {
  color: #00adef;
  letter-spacing: -.1px;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  margin-bottom: 60px;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 24.5px;
}

@media (max-width: 767px) {
  .blog_detail_author_btn {
    margin-bottom: 30px;
    font-size: 1rem;
    line-height: 1.25rem;
  }
}

.blog_detail .recent_post {
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid #666;
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  margin-bottom: 0;
  padding: 30px 0;
}

/*# sourceMappingURL=src_css_Home_BlogDetail_scss_css_e59ae46c._.single.css.map*/