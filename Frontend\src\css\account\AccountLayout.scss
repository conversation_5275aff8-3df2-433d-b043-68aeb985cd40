@use "../theme/var";

.Account_layout {
  &_main {
    display: flex;
  }

  // &_leftaside {

  // }

  &_rightaside {
    width: calc(100% - 355px);
    padding: 2.813rem 1.875rem;
    color: var.$white;
    position: relative;

    @media (min-width: 992px) {
      .mb-lg-4 {
        margin-bottom: 1.875rem !important;
      }
    }

    @media (max-width: 1199px) {
      width: 100%;
      padding: 2.813rem 1rem;
    }
  }
}

.sidebar_heading {
  width: 100%;
  margin-bottom: 26px;

  @media screen and (max-width: 1199px) {
    padding-left: 45px;
  }

  &_top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
  }

  h2 {
    font-size: 48px;
    font-weight: 400;
    font-family: "Gilroy-Bold", sans-serif;
    display: inline-flex;

    @media (max-width: 1199px) {
      font-size: 1.688rem;
    }

    @media (max-width: 767px) {
      font-size: 1.5rem;
    }
  }

  &_icon {
    button {
      background-color: transparent;
      border: 0;
      color: var.$baseclr;
      font-size: 1.15rem;
      padding: 0;
      font-weight: 600;
      transition: all ease-in-out 0.3s;

      @media (max-width: 991px) {
        font-size: 14px;
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }

      .me-2 {
        margin-right: 10px !important;
      }

      .ms-2 {
        margin-left: 10px !important;
      }

      svg {
        margin-left: 10px;
        transition: all ease-in-out 0.3s;
        width: 16px;

        @media (max-width: 991px) {
          width: 14px;
        }

        @media (max-width: 767px) {
          width: 10px;
          margin-left: 0px;
        }

        path {
          fill: var.$baseclr;
        }
      }

      &:hover {
        color: var.$yellow;

        svg {
          margin-left: 12px;

          @media (max-width: 991px) {
            margin-left: 10px;
          }

          path {
            fill: var.$yellow;
          }
        }
      }
    }
  }
}

.common_blackcard,
.common_whitecard {
  background-color: var.$clr191C23;
  padding: 10px;
  border-radius: 15px;
  border: 1px solid var.$clr04498C;
  width: 100%;

  &_innerheader {
    background-color: var.$clr031940;
    padding: 0.625rem 1.25rem;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-family: "Gilroy-Semibold", sans-serif;
    font-weight: 400;

    @media (max-width: 991px) {
      padding: 0.625rem 0.625rem;
    }

    p {
      color: var.$white;

      @media (max-width: 991px) {
        font-size: 14px;
      }

      @media (max-width: 767px) {
        font-size: 14px;
      }
    }

    &_content {
      padding-right: 30px;
      font-size: 18px;
      font-weight: 400;

      h6 {
        font-weight: 600;
      }

      @media (max-width: 991px) {
        padding-right: 15px;

        h6 {
          font-size: 18px !important;
        }
      }

      @media (max-width: 767px) {
        padding-right: 5px;
      }

      @media (max-width: 400px) {
        h6 {
          font-size: 14px !important;
        }
      }
    }

    &_icon {
      button {
        background-color: transparent;
        border: 0;
        color: var.$baseclr;
        font-size: 18px;
        padding: 0;
        font-weight: 600;
        font-family: "Gilroy-Semibold", sans-serif;
        transition: all ease-in-out 0.3s;
        white-space: nowrap;

        @media (max-width: 991px) {
          font-size: 14px;
          display: flex;
          align-items: center;
          justify-content: flex-end;
        }

        .me-2 {
          margin-right: 10px !important;
        }

        .ms-2 {
          margin-left: 10px !important;
        }

        .link-icon {
          flex-shrink: 0;
          height: auto;
        }

        svg {
          transition: all ease-in-out 0.3s;
          width: 16px;

          @media (max-width: 991px) {
            width: 14px;
          }

          @media (max-width: 767px) {
            width: 10px;
            margin-left: 0px;
          }

          path {
            fill: var.$baseclr;
          }
        }

        &:hover {
          color: var.$yellow;

          svg {
            margin-left: 12px;

            @media (max-width: 991px) {
              margin-left: 10px;
            }

            path {
              fill: var.$yellow;
            }
          }
        }
      }
    }

    &_tradeacct {
      min-width: 200px;
      width: 200px;

      h6 {
        background-color: var.$clr04498C;
        padding: 8px 15px;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        color: var.$white;
        font-size: 1.25rem;
        line-height: normal;

        @media (max-width: 991px) {
          font-size: 16px;
        }
      }

      p {
        background-color: var.$white;
        padding: 8px 15px;
        border-bottom-left-radius: 10px;
        border-bottom-right-radius: 10px;
        color: var.$black;
        font-size: 1.25rem;
        font-weight: 600;
        margin: 0;
        line-height: normal;

        @media (max-width: 991px) {
          font-size: 16px;
        }
      }
    }
  }

  &_innerbody {
    padding: 1.25rem 0.625rem;
  }
}

.connetionTable,
.removePadding {

  .common_blackcard_innerbody,
  .common_whitecard_innerbody {
    padding: 0;
  }
}

.account_card {
  &.pullcontent {
    @media (max-width: 991px) {

      .common_blackcard_innerheader_icon,
      .common_whitecard_innerheader_icon {
        margin-top: 10px;
      }
    }
  }
}

.common_whitecard {
  background-color: white !important;

  &_innerbody {
    padding: 0.8rem 0.3rem 0px 0.3rem;
  }
}

.account {
  &_card {
    .label {
      color: var.$white;
      font-size: 1.125rem;
      font-weight: 600;
    }

    &_list {
      &_btns {
        display: flex;
        justify-content: end;
        align-items: center;
        gap: 10px;

        .btn-style {
          min-height: 40px;
          font-size: 16px !important;
        }
      }

      &_form {
        width: 40%;

        .row {
          width: 100% !important;
        }
      }

      ul {
        li {
          display: flex;
          justify-content: space-between;
          margin-bottom: 1.25rem;
          color: var.$white;
          font-size: 1.125rem;
          font-weight: 600;

          @media (max-width: 991px) {
            font-size: 15px;
            margin-bottom: 1rem;
          }

          &:last-child {
            margin-bottom: 0;
          }

          span {
            color: #ffffff;
          }
        }

      }

      p {
        color: var.$lightgreyclr;
      }
    }

    &_redeem {
      .form-control {
        width: calc(100% - 136px);
        margin-right: 10px;
      }

      ::placeholder {
        color: #fff;
        opacity: 1;
      }

      .btn-style {
        font-size: 16px;
        font-weight: 600;
        line-height: 26px;
        min-width: 150px;
        padding: 8px 15px;
        min-height: 56px;

        @media (max-width: 991px) {
          font-size: 14px;
          line-height: 24px;
          min-width: 130px;
          padding: 8px 15px;
          min-height: 52px;
        }
      }

      .error-messages {
        .success {
          color: #32CD33;
        }

        .invalid {
          color: #ff696a;
        }
      }
    }

    &_checkup {
      &_verify {
        width: calc(100% - 150px);
        padding-right: 30px;

        @media (max-width: 991px) {
          width: calc(100% - 100px);
          padding-right: 0;
        }
      }

      &_chart {
        width: 150px;
        text-align: center;

        @media (max-width: 991px) {
          width: 100px;

          .CircularProgressbar_text h6 {
            font-size: 14px;
          }
        }
      }
    }

    &_subscription {
      &_list {
        display: flex;
        flex-wrap: wrap;

        li {
          width: 25%;

          @media (max-width: 767px) {
            width: 50%;
            padding: 5px 0;

            &:nth-child(2n) {
              text-align: right;
            }
          }

          p {
            color: #ffffff;
            font-weight: 600;
            margin-top: 0.5rem;

            @media (max-width: 991px) {
              font-size: 14px;
              line-height: 20px;
            }

            @media (max-width: 767px) {
              margin-top: 0;
            }
          }

          h6 {
            @media (max-width: 991px) {
              font-size: 15px;
            }
          }
        }
      }
    }

    &_table {
      .simple_table_imgIcon {
        min-width: 20px;
      }

      .tableless {
        border: 0;

        &::before {
          display: none;
        }

        .common_table {
          tr {

            th,
            td {
              border: 0;
            }

            td {
              border-bottom: 1px solid var.$borderclr;
              color: var.$white;
              font-weight: 600 !important;
            }
          }

          thead {
            tr {
              border-radius: 15px;

              th {
                background-color: var.$clr031940;
                padding: 10px 15px;
                font-size: 1rem;

                &:first-child {
                  border-top-left-radius: 15px;
                  border-bottom-left-radius: 15px;
                }

                &:last-child {
                  border-top-right-radius: 15px;
                  border-bottom-right-radius: 15px;
                }
              }
            }
          }
        }
      }
    }

    .add_phone_number,
    .add_number,
    .blue_text_btn {
      background-color: transparent;
      border: 0;
      color: var.$baseclr;
      font-size: 1.25rem;
      font-weight: 600;
      line-height: 24.5px;
      letter-spacing: -0.10000000149011612px;
      transition: all ease-in-out 0.3s;
      padding: 0;

      @media (max-width: 991px) {
        font-size: 1rem;
      }

      &:hover {
        color: var.$yellow;
      }
    }

    .add_number,
    .blue_text_btn {

      svg,
      img {
        width: 16px;
        height: 16px;
        margin-right: 10px;

        path {
          fill: var.$baseclr;
        }
      }

      &:hover {
        svg {
          path {
            fill: var.$yellow;
          }
        }
      }
    }
  }
}

.CircularProgressbar_text h6 {
  font-size: 14px !important;
}

.account-custom-select {
  position: relative;

  .header {
    min-height: 56px;
    box-shadow: none;
    outline: none;
    width: 100%;
    padding: 0.5rem 1.25rem;
    border-radius: 1rem;
    background-color: #ffffff4d;
    color: #fff;
    font-size: 1rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;

    span {
      width: 100% !important;
    }
  }

  .body {
    width: 100%;
    padding: 0.5rem 1.25rem;
    position: absolute;
    top: 65px;
    border-radius: 1rem;
    border: 1px solid #ffffff4d;
    background-color: #191c23;
    max-height: 250px;
    overflow-y: scroll;
    z-index: 999;


    .search {
      background-color: #ffffff4d;
      margin: 0.5rem 0;
      padding: 0.5rem 1.25rem;
      width: 100%;
      border-radius: 1rem;
      display: flex;
      gap: 10px;
      align-items: center;

      input {
        background-color: transparent;
        width: 100%;
        color: #fff;

        &:focus-visible {
          outline: none !important;
        }
      }
    }

    ul {
      li {
        cursor: pointer;
        padding: 10px;
        border-radius: 8px;
        border-bottom: 0 !important;
        margin-bottom: 0 !important;

        &:hover {
          background-color: #ffffff4d;
        }
      }
    }
  }
}

.account-custom-select.simple {
  .header {
    border: 1px solid #********;
    background-color: #f2f2f2;
    min-height: 34px;
    padding: 7px 10px;
    border-radius: 10px;

    span {
      color: black !important;
      font-size: 14px;
      font-weight: 600;
    }
  }

  .body {
    width: 396px;
    padding: 10px 12px;
    top: 45px;
    right: 0;
    border-radius: 0.94rem;
    border: 1px solid #0606061a;
    background-color: white;
    max-height: 200px;

    @media only screen and (max-width: 500px) {
      width: auto;
    }

    &.align-left {
      left: 0;
      right: auto;
    }

    &.align-right {
      right: 0;
      left: auto;
    }

    &::-webkit-scrollbar {
      display: block;
    }

    .option-heading {
      display: flex;
      align-items: center;
      gap: 5px;
      margin: 6px 0px 2px 0px;

      p {
        color: black;
        font-size: 14px;
        font-weight: 700;
      }
    }

    ul {
      li {
        font-size: 14px;
        font-weight: 600;
        padding: 4px 0px;
        border-radius: 0 !important;
        border-bottom: 1px solid #********;

        &:hover {
          background-color: rgba(211, 211, 211, 0.166);
        }
      }
    }
  }
}

.width-autofit {
  width: fit-content !important;
}

.text_00ADEF {
  color: #00ADEF !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 28px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #fff;
  transition: 0.4s;
  border-radius: 34px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 22px;
  width: 22px;
  left: 3px;
  bottom: 3px;
  background-color: #9c9a9f;
  transition: 0.4s;
  border-radius: 50%;
}

input:checked+.slider {
  background-color: #0099d1;
}

input:checked+.slider:before {
  transform: translateX(22px);
  background-color: white;
}

.modal_overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 9998;

  .search_section,
  .modal-body {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    width: 1000px;
    background-color: #031940;
    padding: 20px;
    border-radius: 15px;
    max-height: 90vh;
    border: 1px solid #00adef;

    @media (max-width: 1023px) {
      width: 90%;
    }

    h4 {
      font-size: 28px;
      font-weight: 400;
      margin-bottom: 20px;
    }

    .search_header {
      margin-bottom: 20px;

      .closing_Section {
        display: flex;
        align-items: center;
        gap: 1rem;
        flex-shrink: 0;
        height: 100%;

        @media (max-width: 1023px) {
          padding-right: 8px;
        }

        img {
          cursor: pointer;
        }
      }

      .search {
        height: 100%;
      }

      p {
        font-size: 18px;
        font-weight: 600;
        white-space: nowrap;
      }

      span {
        white-space: nowrap;
        color: #ffffff99;
      }

      .btn-style {
        min-width: 100%;
      }

      button {
        span {
          color: #fff !important;
        }
      }
    }

    .search {
      width: 100%;
      background-color: #ffffff33;
      padding: 10px 20px;
      border-radius: 15px;
      display: flex;
      gap: 10px;

      input {
        background-color: transparent;
        width: 100%;
        height: 100%;
        color: #ffffff99;

        &:focus {
          box-shadow: none;
          outline: 0;
        }
      }
    }

    @keyframes fadeOut {
      0% {
        opacity: 0.9;
      }

      50% {
        opacity: 1;
      }

      90% {
        opacity: 0.9;
      }

      100% {
        opacity: 0;
      }
    }

    .modal-footer-btn {
      min-height: 40px !important;
      font-size: 18px;
    }
  }
}