/* [project]/src/css/common/CommonButton.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.btn-style, .btn-primary {
  text-align: center;
  text-transform: capitalize;
  color: #fff;
  background-color: #00adef;
  border: 0;
  border-radius: 10rem;
  justify-content: center;
  align-items: center;
  min-width: 150px;
  min-height: 66px;
  padding: .5rem 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  transition: all .3s ease-in-out;
  display: inline-flex;
}

.btn-style span, .btn-primary span {
  line-height: 1;
}

@media (max-width: 1599px) {
  .btn-style, .btn-primary {
    min-height: 66px;
  }
}

@media (max-width: 1199px) {
  .btn-style, .btn-primary {
    min-height: 56px;
    font-size: 1.125rem;
    font-weight: 500;
  }
}

@media (max-width: 767px) {
  .btn-style, .btn-primary {
    min-height: 46px;
    font-size: 1rem;
  }
}

.btn-style:hover, .btn-primary:hover {
  color: #fff;
  background-color: #0099d1;
}

.btn-style.transparent, .btn-primary.transparent {
  background-color: rgba(0, 0, 0, 0);
  border: none;
}

.btn-style.white-btn, .btn-primary.white-btn {
  color: #000;
  background: #fff;
}

.btn-style.white-btn:hover, .btn-primary.white-btn:hover {
  color: #fff;
  background: #00adef;
}

.btn-style.yellow-btn, .btn-primary.yellow-btn {
  color: #fff;
  background-color: #fea500;
}

.btn-style.yellow-btn:hover, .btn-primary.yellow-btn:hover {
  color: #fff;
  background-color: #c9870d;
}

.btn-style.gray-btn, .btn-primary.gray-btn {
  color: #fff;
  background-color: #5e6165 !important;
}

.btn-style.gray-btn:hover, .btn-primary.gray-btn:hover {
  color: #fff;
  background-color: #708090;
}

.btn-style.gradient-btn, .btn-primary.gradient-btn {
  color: #fff;
  background: linear-gradient(75deg, #00aeef, #1f5aff 50.31%, #da00ff);
}

.btn-style.gradient-btn:hover, .btn-primary.gradient-btn:hover {
  color: #fff;
  background: linear-gradient(75deg, #0043ff, #1f5aff 50.31%, #da00ff);
}

.btn-style.green-btn, .btn-primary.green-btn {
  color: #fff;
  background-color: #32cd33;
}

.btn-style.green-btn:hover, .btn-primary.green-btn:hover {
  color: #fff;
  background-color: #2bb72b;
}

.btn-style.red-btn, .btn-primary.red-btn {
  color: #fff;
  background-color: #ff696a;
}

.btn-style.red-btn:hover, .btn-primary.red-btn:hover {
  color: #fff;
  background-color: #e65f60;
}

.btn-style.border-btn, .btn-primary.border-btn {
  color: #fff;
  background: none;
  border: 1px solid #00adef;
}

.btn-style.border-btn:hover, .btn-primary.border-btn:hover {
  color: #fff;
  background: #00adef;
}

.btn-style .onlyIcon, .btn-primary .onlyIcon {
  margin-right: 15px;
  display: inline-flex;
}

.btn-style:disabled, .btn-style.disabled, .btn-primary:disabled, .btn-primary.disabled {
  color: #fff;
  cursor: not-allowed;
  opacity: 1;
  background: #c5c5d5;
}

:disabled, .disabled {
  color: #fff;
  cursor: not-allowed;
  opacity: 1;
  background-color: #414c60;
}

.white20 {
  background-color: rgba(255, 255, 255, .12);
  width: 100%;
}

/* [project]/src/css/common/Header.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

header {
  z-index: 9998;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
}

.home-page .siteHeader {
  border-bottom: 0;
  background-color: #000 !important;
}

@media (min-width: 1200px) {
  .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show, .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show:after, .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover:after {
    transform: rotate(180deg);
  }
}

.home-page .siteHeader .navMenu .common_dropdown.dropdown.show .dropdown-toggle:after {
  transform: rotate(180deg);
}

@media (min-width: 1200px) {
  .home-page .siteHeader .navMenu .common_dropdown .dropdown-menu {
    background-color: #1e222d !important;
  }
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.active, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus {
  background-color: #2a2e39 !important;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon {
  color: #00b9ff;
  font-weight: 700;
  transition: none;
  background: linear-gradient(to right, #2a2e39, #1e222d) !important;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path {
  fill: #00b9ff;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
  background: #2a2e39 !important;
}

@media (max-width: 1199px) {
  .home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon {
    color: #00aeef;
    font-weight: 700;
    transition: none;
    background: linear-gradient(to right, #000, #2d2d2d) !important;
  }

  .home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path {
    fill: #00b9ff;
  }

  .home-page .siteHeader .navbar-collapse {
    background-color: rgba(0, 0, 0, .9) !important;
  }
}

@media (min-width: 1200px) {
  .home-page .siteHeader .navbar-collapse .nav-link:hover, .home-page .siteHeader .navbar-collapse .nav-link.active, .home-page .siteHeader .navbar-collapse .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .languageDropdown {
    width: 64px;
  }
}

@media (min-width: 1200px) and (max-width: 1199px) {
  .home-page .languageDropdown, .home-page .languageDropdown .common_dropdown {
    width: 100%;
  }
}

@media (min-width: 1200px) {
  .home-page .languageDropdown .common_dropdown .nav-link:hover, .home-page .languageDropdown .common_dropdown .nav-link.active, .home-page .languageDropdown .common_dropdown .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
    background: #2a2e39 !important;
  }
}

.siteHeader {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background-color: #031940;
  border-bottom: 1px solid #064197;
  justify-content: center;
  align-items: center;
  height: 80px;
  padding: 1rem 0;
  display: flex;
}

.siteHeader .btn-style {
  min-width: 169px;
  min-height: 56px;
}

@media (max-width: 1199px) {
  .siteHeader .btn-style {
    min-width: 120px;
    min-height: 40px;
    padding: 8px 1rem;
    font-size: 14px;
  }
}

@media (max-width: 575px) {
  .siteHeader .btn-style {
    min-width: 80px;
    min-height: 34px;
    font-size: 14px;
  }
}

@media (max-width: 1199px) {
  .siteHeader {
    z-index: 9999;
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
}

@media (max-width: 767px) {
  .siteHeader {
    padding: .625rem 0;
  }
}

.siteHeader .navbar {
  width: 100%;
  padding: 0;
}

.siteHeader .navbar .brandLogo img {
  width: 100%;
  max-width: 190px;
}

@media (max-width: 767px) {
  .siteHeader .navbar .brandLogo img {
    max-width: 150px;
    margin-right: 0;
  }
}

@media (max-width: 360px) {
  .siteHeader .navbar .brandLogo img {
    max-width: 120px;
    margin-right: 0;
  }
}

.siteHeader .navbar-collapse {
  height: auto !important;
}

.siteHeader .navbar-collapse .nav-link {
  white-space: nowrap;
  color: #fff;
  background-color: rgba(0, 0, 0, 0);
  align-items: center;
  padding: .5rem 1.5rem;
  font-size: 1.25rem;
  font-weight: 400;
  display: flex;
}

.siteHeader .navbar-collapse .nav-link:hover, .siteHeader .navbar-collapse .nav-link.active, .siteHeader .navbar-collapse .nav-link:focus {
  color: #00adef;
}

@media (min-width: 1200px) {
  .siteHeader .navbar-collapse .nav-link:hover, .siteHeader .navbar-collapse .nav-link.active, .siteHeader .navbar-collapse .nav-link:focus {
    color: #fff;
    background-color: #283f67 !important;
  }

  .siteHeader .navbar-collapse .nav-link {
    margin: 0 3px;
  }
}

@media (max-width: 1199px) {
  .siteHeader .navbar-collapse .nav-link {
    border-bottom: 1px solid rgba(255, 255, 255, .2);
    padding: 1.25rem 0;
    font-size: 1.125rem;
  }

  .siteHeader .navbar-collapse .nav-link img {
    width: 22px;
  }

  .siteHeader .navbar-collapse .nav-link.white_stroke_icon {
    color: #00aeef;
    background: linear-gradient(to right, #031940, #283f67);
    font-weight: 700;
    transition: none;
  }

  .siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path {
    fill: #00b9ff;
  }

  .siteHeader .navbar-collapse {
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    z-index: 9999;
    background-color: rgba(3, 25, 64, .9);
    width: 350px;
    padding: 0;
    transition: all .2s ease-in-out;
    display: block;
    position: fixed;
    top: 0;
    left: -350px;
    height: 100vh !important;
  }

  .siteHeader .navbar-collapse a {
    text-align: left;
    justify-content: flex-start;
    display: flex;
  }

  .siteHeader .navbar-collapse.show {
    height: 100vh;
    left: 0;
  }

  .siteHeader .navbar-collapse .navMenu {
    max-height: calc(100vh - 84px);
    max-height: calc(100dvh - 84px);
    padding: 20px;
    overflow-y: auto;
  }
}

@media (max-width: 767px) {
  .siteHeader .navbar-collapse {
    width: 100%;
    left: -100%;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle {
  border-radius: 0;
  padding: .5rem 1.5rem !important;
}

@media (max-width: 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle {
    border-bottom: 1px solid rgba(255, 255, 255, .2);
    width: 100%;
    padding: 1.25rem 0 !important;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:after {
  background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 1.15rem;
  border: 0;
  width: 1.15rem;
  height: 1.15rem;
  margin-left: 1rem;
  transition: all .3s ease-in-out;
  display: block;
}

@media (max-width: 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:after {
    margin-left: 0;
    position: absolute;
    right: 0;
  }
}

@media (min-width: 1200px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show, .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover {
    color: #fff;
    background-color: #283f67;
  }

  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show:after, .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover:after {
    transform: rotate(180deg);
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown.show .dropdown-toggle:after {
  transform: rotate(180deg);
}

@media screen and (max-width: 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu {
    background-color: rgba(0, 0, 0, 0);
    border: 0;
    padding: 0;
    position: static;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link {
  align-items: start;
  padding: .875rem 1.5rem;
  font-weight: 400 !important;
}

@media screen and (max-width: 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link {
    padding: .875rem 1rem;
  }
}

.siteHeader .navbar .navbar-toggler {
  background-color: rgba(0, 0, 0, 0);
  width: 24px;
  height: 18px;
  margin-left: 0;
  padding: 0;
  position: relative;
}

.siteHeader .navbar .navbar-toggler:focus {
  box-shadow: none;
}

@media (max-width: 1199px) {
  .siteHeader .navbar .navbar-toggler {
    margin-right: 13px;
  }
}

@media (max-width: 767px) {
  .siteHeader .navbar .navbar-toggler {
    margin-right: 13px;
  }
}

.siteHeader .navbar .navbar-toggler:after {
  content: "";
  background-color: #fff;
  width: 24px;
  height: 2px;
  transition: all .3s ease-in-out;
  position: absolute;
  bottom: 0;
  left: 0;
}

.siteHeader .navbar .navbar-toggler:before {
  content: "";
  background-color: #fff;
  width: 24px;
  height: 2px;
  transition: all .3s ease-in-out;
  position: absolute;
  top: 0;
  left: 0;
}

.siteHeader .navbar .navbar-toggler .navbar-toggler-icon {
  background-color: #fff;
  background-image: none;
  width: 24px;
  height: 2px;
  transition: all .3s ease-in-out;
  display: flex;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  border-radius: .625rem;
  align-items: center;
  padding: 0;
  font-size: 1.25rem;
  display: flex;
  padding: .5rem .2rem !important;
}

@media (max-width: 991px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1.125rem;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle:after {
  display: none;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle.show svg path {
  fill: #00adef;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu {
  border: 1px solid rgba(255, 255, 255, .3);
  border-radius: .625rem;
  min-width: 200px;
  position: absolute;
  top: 45px;
}

@media screen and (max-width: 1199px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu {
    min-width: 100%;
    padding: 0;
    position: static;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item {
  color: #fff;
  padding: .625rem 1rem;
  font-size: 1.125rem;
  font-weight: 600;
}

@media (max-width: 991px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item {
    font-size: 1rem;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item svg, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item img {
  margin-right: 10px;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.active, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus {
  background: #283f67;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon {
  color: #00b9ff;
  background: linear-gradient(to right, #283f67, #031940);
  font-weight: 700;
  transition: none;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path {
  fill: #00b9ff;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
  background: #283f67 !important;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show svg, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show img {
  width: 18px;
}

@media screen and (max-width: 1199px) {
  .siteHeader .navbar .openmenuSidebar {
    border-bottom: 1px solid rgba(255, 255, 255, .5);
    padding: 30px 15px;
  }

  .siteHeader .navbar .openmenuSidebar .brandLogo {
    padding: 0;
  }

  .siteHeader .navbar .openmenuSidebar .brandLogo img {
    max-width: 150px;
  }

  .siteHeader .navbar .openmenuSidebar .navbar-toggler {
    position: absolute;
    right: 15px;
  }
}

.siteHeader.openmenu .navbar .navbar-toggler:after {
  background-color: #fff;
  transform: rotate(45deg)translate(-5px, -5px);
}

.siteHeader.openmenu .navbar .navbar-toggler:before {
  background-color: #fff;
  transform: rotate(-45deg)translate(-5px, 5px);
}

.siteHeader.openmenu .navbar .navbar-toggler .navbar-toggler-icon {
  opacity: 0;
}

.siteHeader .user_icon img {
  width: 26px;
  height: 26px;
}

@media screen and (max-width: 767px) {
  .siteHeader .sidebar_backdrop {
    display: none;
  }
}

.languageDropdown {
  width: 64px;
}

@media (max-width: 1199px) {
  .languageDropdown, .languageDropdown .common_dropdown {
    width: 100%;
  }
}

.languageDropdown .common_dropdown .nav-link:hover, .languageDropdown .common_dropdown .nav-link.active, .languageDropdown .common_dropdown .nav-link:focus {
  color: #fff;
  background-color: #283f67 !important;
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  align-items: center;
  padding: 0;
  font-size: 1.25rem;
  display: flex;
  border-radius: 0 !important;
}

@media (max-width: 991px) {
  .languageDropdown .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1rem;
  }
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle svg {
  margin-right: 10px;
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle:focus, .languageDropdown .common_dropdown.dropdown .dropdown-toggle:hover {
  background-color: rgba(0, 0, 0, 0) !important;
}

@media (max-width: 1199px) {
  .languageDropdown .common_dropdown.dropdown .dropdown-toggle {
    width: 100%;
  }
}

.languageDropdown .globalIcon .icon {
  transition: opacity .3s;
}

.languageDropdown .globalIcon .blue, .languageDropdown .nav-item:hover .globalIcon .black, .languageDropdown .nav-item.show .globalIcon .black {
  display: none;
}

.languageDropdown .nav-item:hover .globalIcon .blue, .languageDropdown .nav-item.show .globalIcon .blue {
  display: block;
}

.userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name {
  display: none;
}

@media screen and (max-width: 1199px) {
  .userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name {
    padding-left: 10px;
    font-size: 18px;
    display: block;
  }

  .userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name svg {
    width: 26px;
    height: 26px;
  }

  .userDropdown.common_dropdown.dropdown .dropdown-toggle {
    border-bottom: 0 !important;
  }
}

.userDropdown.common_dropdown.dropdown .dropdown-toggle:hover {
  background-color: rgba(0, 0, 0, 0) !important;
}

@media (max-width: 1199px) {
  .brandLogo {
    display: flex;
  }

  .brandLogo img {
    max-width: 150px;
  }
}

@media (max-width: 767px) {
  .brandLogo img {
    max-width: 110px;
  }
}

@media (max-width: 359px) {
  .brandLogo img {
    max-width: 100px;
  }
}

.sidebar_backdrop {
  z-index: 1000;
  background-color: rgba(0, 0, 0, .2);
  width: 100%;
  height: 100vh;
  transition: all .2s ease-in-out;
  position: fixed;
  top: 0;
  left: 0;
}

.image_color_to_white {
  filter: brightness(0) invert();
}

@media (min-width: 1200px) {
  .nav-link:hover, .nav-link.active, .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }
}

/* [project]/src/css/common/Footer.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.site_footer {
  background-color: #000;
}

.site_footer_inner {
  padding: 70px 0;
}

@media screen and (max-width: 991px) {
  .site_footer_inner {
    padding: 40px 0;
  }
}

.site_footer_logo img {
  width: 200px;
}

.site_footer_content p {
  color: rgba(255, 255, 255, .65);
  letter-spacing: -.1px;
  margin-top: 20px;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
}

@media screen and (max-width: 991px) {
  .site_footer_content p {
    font-size: 16px;
  }
}

@media screen and (max-width: 767px) {
  .site_footer_links {
    margin-top: 20px;
  }
}

.site_footer_links h4 {
  color: #c5c5d5;
  margin-bottom: 1.25rem;
  font-size: 1.65rem;
  font-weight: 600;
  line-height: 35px;
}

@media screen and (max-width: 991px) {
  .site_footer_links h4 {
    font-size: 18px;
  }
}

.site_footer_links ul li a {
  letter-spacing: -.1px;
  color: #fff;
  padding-bottom: 10px;
  font-size: 20px;
  font-weight: 600;
  line-height: 24.5px;
  transition: all .3s ease-in-out;
}

@media screen and (max-width: 991px) {
  .site_footer_links ul li a {
    font-size: 16px;
  }
}

.site_footer_links ul li a:hover, .site_footer_links ul li a.active {
  color: #00adef;
}

.site_footer_copyright {
  border-top: 1px solid #fff;
  padding: 1.25rem 0;
}

.site_footer_copyright p {
  text-align: center;
  letter-spacing: -.1px;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
}

@media screen and (max-width: 991px) {
  .site_footer_copyright p {
    font-size: 16px;
  }
}

/* [project]/src/css/Home/Blog.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.blog {
  padding: 5rem 0;
  position: relative;
}

.blog_cards {
  margin-bottom: 50px !important;
}

@media (max-width: 767px) {
  .blog_cards {
    margin-bottom: 20px !important;
  }
}

@media (max-width: 575px) {
  .blog_cards {
    margin-bottom: 10px !important;
  }
}

.blog_cards .slider-container {
  position: relative;
}

.blog_cards .slider-container .slick-slider {
  margin: 0 -15px;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track {
  display: flex;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track .slick-slide {
  height: auto;
  padding: 0 15px;
  display: flex;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track .slick-slide > div {
  width: 100%;
  display: flex;
}

.blog_cards .slider-container .slick-slider .slick-list .slick-track .slick-slide.slick-active {
  padding-top: 0;
}

.blog_postcard {
  cursor: pointer;
  background-color: #032251;
  border: 1px solid rgba(255, 255, 255, .5);
  border-radius: 1.25rem;
  height: 100%;
  position: relative;
}

@media (max-width: 767px) {
  .blog_postcard {
    margin-bottom: 1.25rem;
  }
}

.blog_postcard:hover .blog_postcard_img img {
  transform: scale(1.1);
}

.blog_postcard_img {
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
  position: relative;
  overflow: hidden;
}

.blog_postcard_img:before {
  content: "";
  background-color: rgba(255, 255, 255, .6);
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
  width: 100%;
  padding-top: 54.25%;
  display: block;
}

.blog_postcard_img_overlay {
  height: 100%;
  margin-left: auto;
  margin-right: auto;
  display: block;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  overflow: hidden;
}

.blog_postcard_img img {
  object-fit: cover;
  border-top-left-radius: 1.25rem;
  border-top-right-radius: 1.25rem;
  width: 100%;
  height: 100%;
  transition: all .3s ease-in-out;
}

.blog_postcard_content {
  padding: .65rem 1.25rem 2rem;
}

@media (max-width: 1199px) {
  .blog_postcard_content {
    padding: .65rem 1rem;
  }
}

.blog_postcard_content h3 {
  letter-spacing: -.1px;
  color: #c5c5d5;
  margin-bottom: .625rem;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 1.5rem;
}

@media (max-width: 1199px) {
  .blog_postcard_content h3 {
    font-size: 1rem;
    line-height: 1.25rem;
  }
}

.blog_postcard_content p {
  letter-spacing: -.1px;
  color: #fff;
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 2rem;
}

@media (max-width: 1199px) {
  .blog_postcard_content p {
    font-size: 15px;
    line-height: 23px;
  }
}

@media (max-width: 767px) {
  .blog .slider-container .slick-slider .slick-arrow.slick-prev {
    top: 100px !important;
    left: -45px !important;
  }
}

.blog .slider-container .slick-slider .slick-arrow.slick-prev.slick-disabled {
  opacity: .7;
  background-color: #414c60;
}

@media (max-width: 767px) {
  .blog .slider-container .slick-slider .slick-arrow.slick-next {
    top: 100px !important;
    right: -30px !important;
  }
}

.blog .slider-container .slick-slider .slick-arrow.slick-next.slick-disabled {
  opacity: .7;
  background-color: #414c60;
}

.recent_post {
  cursor: pointer;
  background-color: #032251;
  border: 1px solid rgba(8, 5, 5, .5);
  border-radius: 1.25rem;
  flex-wrap: wrap;
  width: 100%;
  margin-bottom: 30px;
  display: flex;
  position: relative;
}

.recent_post h1 {
  margin-bottom: 50px;
  font-size: 3rem;
  font-weight: 800;
}

@media (max-width: 767px) {
  .recent_post h1 {
    margin-bottom: 30px;
  }

  .recent_post {
    margin-bottom: 1.25rem;
  }
}

.recent_post_img {
  border-top-left-radius: 1.25rem;
  border-bottom-left-radius: 1.25rem;
  width: 366px;
  position: relative;
  overflow: hidden;
}

@media (max-width: 991px) {
  .recent_post_img {
    width: 40%;
  }
}

@media (max-width: 599px) {
  .recent_post_img {
    border-top-left-radius: 1.25rem;
    border-top-right-radius: 1.25rem;
    border-bottom-left-radius: 0;
    width: 100%;
  }

  .recent_post_img:before {
    content: "";
    background-color: rgba(255, 255, 255, 0);
    border-top-left-radius: 1.25rem;
    border-top-right-radius: 1.25rem;
    width: 100%;
    padding-top: 54.25%;
    display: block;
  }
}

.recent_post_img img {
  object-fit: cover;
  width: 100%;
  height: 100%;
}

@media (max-width: 767px) {
  .recent_post_img img {
    height: 100%;
    margin-left: auto;
    margin-right: auto;
    display: block;
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    overflow: hidden;
  }
}

.recent_post_content {
  word-wrap: break-word;
  overflow-wrap: break-word;
  width: calc(100% - 366px);
  padding: 2rem 1.25rem;
}

@media (max-width: 991px) {
  .recent_post_content {
    width: 60%;
    padding: 1rem 1.25rem;
  }
}

@media (max-width: 599px) {
  .recent_post_content {
    width: 100%;
    padding: 2rem 1rem;
  }
}

.recent_post_content small {
  display: block;
}

.recent_post_content h4 {
  margin-top: .625rem;
  margin-bottom: .625rem;
}

.recent_post_content p, .recent_post_content small {
  color: #c5c5d5;
  font-size: 1.125rem;
  font-weight: 400;
  line-height: 27px;
}

@media (max-width: 767px) {
  .recent_post_content p, .recent_post_content small {
    font-size: 1rem;
  }
}

.recent_post_time {
  color: #00adef;
  margin-top: 10px;
  font-size: 1rem;
  font-weight: 600;
  line-height: 26px;
  display: block;
}

@media (max-width: 767px) {
  .recent_post_time {
    font-size: .9rem;
  }
}

.recontPostTitle {
  margin-bottom: 50px;
  font-size: 3rem;
  font-weight: 800;
}

@media (max-width: 767px) {
  .recontPostTitle {
    margin-bottom: 30px;
    font-size: 1.5rem;
  }
}

.slick-slider .slick-prev, .slick-slider .slick-next {
  cursor: pointer;
  color: rgba(0, 0, 0, 0);
  background: none;
  border: none;
  outline: none;
  width: 20px;
  height: 20px;
  padding: 0;
  display: block;
  top: 50%;
  -webkit-transform: translate(0, -50%) !important;
  -ms-transform: translate(0, -50%) !important;
  position: absolute !important;
  transform: translateY(-50%) !important;
}

/* [project]/src/css/Home/BlogDetail.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.blog_detail_tag {
  text-align: center;
  letter-spacing: -.1px;
  text-transform: uppercase;
  color: #fff;
  background-color: #00adef;
  border: 0;
  border-radius: 10px;
  padding: 6px 20px;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
}

.blog_detail_heading h1 {
  color: #fff;
  padding: 30px 0;
  font-size: 2.8rem;
  font-weight: 600;
}

@media (max-width: 1199px) {
  .blog_detail_heading h1 {
    font-size: 1.5rem;
  }
}

@media (max-width: 767px) {
  .blog_detail_heading h1 {
    font-size: 1.5rem;
    line-height: 35px;
  }
}

.blog_detail_heading h5 {
  color: #fff;
  padding-top: 30px;
  font-size: 1.25rem;
  font-weight: 600;
}

@media (max-width: 767px) {
  .blog_detail_heading h5 {
    padding-top: 5px;
    font-size: 1.125rem;
    line-height: 30px;
  }
}

.blog_detail_postimg {
  padding: 5rem 0;
}

@media (max-width: 767px) {
  .blog_detail_postimg {
    padding: 2rem 0;
  }
}

.blog_detail_postimg img {
  border-radius: 60px;
  width: 100%;
}

@media (max-width: 767px) {
  .blog_detail_postimg img {
    border-radius: 30px;
  }
}

.blog_detail_text p {
  letter-spacing: -.1px;
  color: #fff;
  white-space: normal;
  word-wrap: break-word;
  text-overflow: clip;
  max-width: 1000px;
  padding-top: 20px;
  font-size: 1.5rem;
  font-weight: 400;
  line-height: 36px;
  overflow: visible;
}

@media (max-width: 767px) {
  .blog_detail_text p {
    padding-top: 0;
    font-size: 1rem;
    line-height: 24px;
  }
}

.blog_detail_author {
  padding-top: 5rem;
}

@media (max-width: 767px) {
  .blog_detail_author {
    padding-top: 3rem;
  }
}

.blog_detail_author_btn {
  color: #00adef;
  letter-spacing: -.1px;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  margin-bottom: 60px;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 24.5px;
}

@media (max-width: 767px) {
  .blog_detail_author_btn {
    margin-bottom: 30px;
    font-size: 1rem;
    line-height: 1.25rem;
  }
}

.blog_detail .recent_post {
  background-color: rgba(0, 0, 0, 0);
  border: 1px solid #666;
  border-left: 0;
  border-right: 0;
  border-radius: 0;
  margin-bottom: 0;
  padding: 30px 0;
}

/* [project]/src/css/common/CustomBreadcrumb.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.custom_breadcrumb .secondary_link {
  cursor: pointer;
}

.custom_breadcrumb .breadcrumb li a {
  color: #c5c5d5;
}

.custom_breadcrumb .breadcrumb li a:hover {
  color: #00adef;
}

.custom_breadcrumb .breadcrumb li.active, .custom_breadcrumb .breadcrumb li:before {
  color: #fff;
}

/*# sourceMappingURL=src_css_1e68ecdd._.css.map*/