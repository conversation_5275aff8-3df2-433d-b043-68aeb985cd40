'use client';
import React, { useState, useEffect } from 'react';
import { Col, Row } from 'react-bootstrap';
import { PlusIconSvg, EditIconSvg } from '@/assets/svgIcons/SvgIcon';
import { useSelector } from 'react-redux';
import { get } from '@/utils/apiUtils';
import { maskPhone } from '@/utils/phoneMask';
import Link from 'next/link';

export default function PhoneNumber({ userData }) {
    const reduxUser = useSelector((state) => state?.auth?.user || null);

    // Return null if no userData is provided
    if (!userData) {
        return null;
    }

    const getDisplayPhoneNumber = () => {
        if (!userData?.phone_number) return 'Not set';
        return maskPhone(userData.phone_number);
    };

    const hasPhoneNumber = userData?.phone_number;
    return (
        <>
            <Col lg={12} xs={12} className="mb-3 mb-lg-4">
                <div className="common_blackcard account_card">
                    <div className="common_blackcard_innerheader">
                        <div className="common_blackcard_innerheader_content">
                            <h6>Phone Number</h6>
                            <p>Provide your number to receive occasional updates, exclusive offers, or important notifications. We will never share your number without consent.</p>
                        </div>
                        <div className="common_blackcard_innerheader_icon">
                            <Link href="/account/phone/setup?from=/account/details" prefetch={true}>
                                <button className="d-flex align-items-center">
                                    {hasPhoneNumber ? <EditIconSvg /> : <PlusIconSvg />}
                                    <span className="ms-2">{hasPhoneNumber ? 'Update' : 'Add Phone Number'}</span>
                                </button>
                            </Link>
                        </div>
                    </div>
                    <div className="common_blackcard_innerbody">
                        <div className="account_card_list">
                            <ul>
                                <li>
                                    <Col xs={12} md={3}>
                                        <span>Phone Number </span>{" "}
                                    </Col>
                                    <Col xs={12} md={9}>
                                        <span>{getDisplayPhoneNumber()}</span>
                                    </Col>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </Col>
        </>
    )
}
