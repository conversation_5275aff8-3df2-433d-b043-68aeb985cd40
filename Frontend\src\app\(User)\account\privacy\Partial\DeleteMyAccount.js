'use client';
import React, { useState } from "react";
import { Col } from "react-bootstrap";
import DeleteAccountModal from "./DeleteAccountModal";


export default function DeleteMyAccount() {
    const [isConfirmationModal, setConfirmationModal] = useState(false);
    const [isDeletionRequest, setDeletionRequest] = useState(false);
    const [modalType, setModalType] = useState("");
    const [oneTimeMessage, setOneTimeMessage] = useState("");



    const showConfirmationModal = (type) => {
        setModalType(type);
        setConfirmationModal(true);
    }

    const updateResponse = () => {
        setConfirmationModal(false);

        if (modalType === "delete") {
            setDeletionRequest(true);
            setOneTimeMessage("delete");
        } else if (modalType === "cancel") {
            setDeletionRequest(false);
            setOneTimeMessage("cancel");
        }
    }

    const hideConfirmationModal = () => {
        setConfirmationModal(false);
        setModalType("");
    }

    const deletionDate = new Date();
    deletionDate.setDate(deletionDate.getDate() + 30);

    const formattedDeletionDate = deletionDate.toLocaleDateString("en-US", {
        year: "numeric",
        month: "long",
        day: "numeric"
    });

    return (
        <Col lg={12} xs={12} className="mb-3 mb-lg-4 delete_my_account">
            <div className="common_blackcard account_card">
                <div className="common_blackcard_innerheader">
                    <div className="common_blackcard_innerheader_content">
                        <h6>⚠️ {isDeletionRequest ? 'Cancel Deletion Request' : 'Delete My Account'}</h6>
                    </div>
                </div>

                <div className="common_blackcard_innerbody">
                    <div className="account_card_list">
                        <p className='mb-2'>This will permanently delete your TradeReply account and all associated data, including your trades, marketplace listings, strategies, subscriptions, and linked Trade Accounts.</p>
                        <p className='mb-2'>Your account will be <span className='fw-700'>scheduled for deletion in 30 days.</span> During this time, you can still access your account and cancel the deletion request at any point.</p>
                        <p className='mb-2'>Some limited data — such as past marketplace transactions, financial records, and system logs — may be retained as required by law or to preserve the integrity of the platform. For full details, see our <a href="/privacy" target="_blank">Privacy Policy</a>.</p>
                        <p className='mb-2 fw-700'>⚠️ After 30 days, your data will be permanently deleted and cannot be recovered.</p>
                        {isDeletionRequest ?
                            (
                                <button className='btn-style red-btn'
                                    onClick={() => showConfirmationModal("cancel")}>
                                    Cancel Account Deletion
                                </button>

                            )
                            :
                            (
                                <button className='btn-style red-btn'
                                    onClick={() => showConfirmationModal("delete")}>
                                    Delete My Account
                                </button>
                            )
                        }
                    </div>
                </div>
            </div>
            {oneTimeMessage === "delete" && (
                <div className="one-time-message mt-3">
                    <p className="fw-700">Your account is scheduled for deletion.</p>
                    <p>
                        It will be permanently deleted in <span className="fw-700">30 days (on {formattedDeletionDate})</span> unless you cancel the request.
                    </p>
                </div>
            )}

            {oneTimeMessage === "cancel" && (
                <div className="one-time-message mt-3">
                    <p className="fw-700">Your deletion request has been canceled. Your account will remain active.</p>
                </div>
            )}

            {
                isConfirmationModal && (
                    <DeleteAccountModal
                        type={modalType}
                        toggleModal={updateResponse}
                        closeModal={hideConfirmationModal}
                    />
                )
            }
        </Col >
    )
}
