<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Security Verification Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the security verification
    | system including cookie settings, expiration times, and security options.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Cookie Configuration
    |--------------------------------------------------------------------------
    |
    | Basic cookie settings with sensible defaults and minimal customization.
    |
    */

    'cookie' => [
        // Cookie name for security verification
        'name' => 'security_verified',

        // Cookie expiration time in minutes
        'expires_minutes' => 5,

        // Cookie path
        'path' => '/',

        // HTTP Only flag (false so frontend can read it)
//        'http_only' => !(env('APP_ENV') == 'local'),
        'http_only' => false,

        // Secure flag (auto-detected based on environment)
//        'secure' => !(env('APP_ENV') == 'local'), // null = auto-detect, true = force HTTPS, false = allow HTTP
        'secure' => null, // null = auto-detect, true = force HTTPS, false = allow HTTP

        // SameSite attribute
        'same_site' => 'Lax',
    ],

    /*
    |--------------------------------------------------------------------------
    | Remember 2FA Cookie Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for the "Remember 2FA" cookie that allows users to bypass
    | 2FA verification for a specified period after successful verification.
    |
    */

    'remember_2fa' => [
        // Cookie name prefix for remember 2FA (will be suffixed with user hash)
        'name_prefix' => 'remember_2fa',

        // Cookie expiration time in days (30 days)
        'expires_days' => 30,

        // Cookie path
        'path' => '/',

        // HTTP Only flag (false for cross-platform compatibility)
        'http_only' => false,

        // Secure flag (auto-detected based on environment)
        'secure' => null, // null = auto-detect, true = force HTTPS, false = allow HTTP

        // SameSite attribute for CSRF protection
        'same_site' => 'Lax',

        // Cookie priority (High for important security cookies)
        'priority' => 'High',

        // Use encryption instead of signing for enhanced security
        'encrypt_cookies' => true,

        // Domain configuration (auto-detected based on environment)
        'domain' => null, // null = auto-detect, '.tradereply.com' for production subdomains

        // Maximum cookie size warning threshold (4KB limit)
        'max_size_warning' => 3072, // 3KB warning threshold
    ],

    /*
    |--------------------------------------------------------------------------
    | Cryptographic Security
    |--------------------------------------------------------------------------
    |
    | Enable cryptographic signing to prevent cookie tampering.
    |
    */

    'crypto' => [
        // Enable cryptographic signing of cookie values
        'sign_cookies' => true,

        // Algorithm for signing
        'signing_algorithm' => 'sha256',
    ],

    /*
    |--------------------------------------------------------------------------
    | Session Configuration
    |--------------------------------------------------------------------------
    |
    | Basic session and rate limiting settings.
    |
    */

    'session' => [
        // Verification session expiration in minutes (now stored in database)
        'cache_expires_minutes' => 15,

        // Rate limiting for resend requests in seconds
        'resend_rate_limit_seconds' => 60,

        // Maximum verification attempts before lockout
        'max_attempts' => 5,

        // Lockout duration in minutes after max attempts
        'lockout_minutes' => 15,
    ],

    /*
    |--------------------------------------------------------------------------
    | reCAPTCHA Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for reCAPTCHA verification on login attempts.
    |
    */

    'recaptcha' => [
        // Number of failed login attempts before reCAPTCHA is required
        // This applies to both existing and non-existent user accounts
        'threshold' => env('RECAPTCHA_THRESHOLD', 3),
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment Detection
    |--------------------------------------------------------------------------
    |
    | Simple environment detection for cookie domain and security settings.
    |
    */

    'environment' => [
        // Development domains (for auto-detection)
        'development_domains' => [
            'localhost',
            '127.0.0.1',
            '::1',
            '.local',
            '.dev',
            '.test',
        ],

        // Production domains (for auto-detection)
        'production_domains' => [
            'tradereply.com',
            '.tradereply.com',
        ],
    ],

];
