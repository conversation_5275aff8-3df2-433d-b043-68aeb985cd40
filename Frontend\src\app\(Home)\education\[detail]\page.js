import { unstable_noStore as noStore } from 'next/cache';
import EducationContent from "./components/EducationContent";
import HomeLayout from "@/Layouts/HomeLayout";
// import MetaHead from "@/Seo/Meta/MetaHead"; // (unused)
import { Container } from "react-bootstrap";
import JsonLdSchema from "@/Seo/Schema/JsonLdSchema";

// --- Helper: keep outside the component ---
function generateEducationArticleSchema(detailSlug, articleData, baseUrl = "https://www.tradereply.com") {
  const created = articleData?.created_at
    ? new Date(articleData.created_at).toISOString()
    : new Date().toISOString();

  const modified = articleData?.updated_at
    ? new Date(articleData.updated_at).toISOString()
    : created;

  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": `${baseUrl}/education/${detailSlug}`,
    },
    "headline": articleData?.title || "",
    "description": articleData?.summary || "",
    "author": { "@type": "Organization", "name": "TradeReply" },
    "publisher": {
      "@type": "Organization",
      "name": "TradeReply",
      "logo": {
        "@type": "ImageObject",
        "url": "https://cdn.tradereply.com/main/misc/tradereply-public-logo-search.png",
      },
    },
    "datePublished": created,
    "dateModified": modified,
    "articleSection": "Education",
    "articleBody": articleData?.articleBody || "",
  };
}

async function fetchEducationDetail(detail) {
  const url = new URL(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/article/education/${detail}`);
  const res = await fetch(url.toString(), { cache: "no-store" });
  if (!res.ok) throw new Error(`API error: ${res.status}`);
  return res.json();
}

export async function generateMetadata({ params }) {
  noStore();
  const detailSlug = params.detail;

  // Wrap in try/catch so metadata doesn’t hard-fail if API hiccups
  try {
    const data = await fetchEducationDetail(detailSlug);
    const environment = process.env.NEXT_PUBLIC_ENVIRONMENT;

    return {
      title: `What is ${data?.data?.title} | TradeReply Education`,
      description: data?.data?.summary,
      openGraph: {
        title: `What is ${data?.data?.title} | TradeReply Education`,
        description: data?.data?.summary,
        images: [{
          url: data?.data?.feature_image_url || 'https://www.tradereply.com/images/tradereply-trading-analytics-og.jpg',
          width: 1200,
          height: 630,
        }],
      },
      twitter: {
        title: `What is ${data?.data?.title} | TradeReply Education`,
        description: data?.data?.summary,
        site: '@JoinTradeReply',
        images: [data?.data?.feature_image_url || 'https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png'],
      },
      icons: {
        icon: [
          {
            url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.ico`,
            type: "image/x-icon",
          },
          {
            url: `https://cdn.tradereply.com/${environment}/site-assets/tradereply-favicon.svg`,
            type: "image/svg+xml",
          },
        ],
      },
    };
  } catch {
    return {
      title: "TradeReply Education",
      description: "Explore trading education on TradeReply.",
    };
  }
}

export default async function EducationDetail({ params }) {
  noStore();

  const detailSlug = params.detail;
  const data = await fetchEducationDetail(detailSlug);
  const articleData = data.data;

  const educationSchema = generateEducationArticleSchema(detailSlug, articleData);
  const schemas = [educationSchema]; // 🔹 ensure it's an array

  return (
    <>
      <JsonLdSchema schemas={schemas} />
      <HomeLayout>
        <Container>
          <EducationContent
            detailSlug={detailSlug}
            articleData={articleData}
            nextArticle={data.next_article}
            avgProgress={data.avgProgress}
          />
        </Container>
      </HomeLayout>
    </>
  );
}