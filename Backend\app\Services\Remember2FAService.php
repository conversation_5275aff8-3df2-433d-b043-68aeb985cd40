<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Crypt;
use Carbon\Carbon;

class Remember2FAService
{
    /**
     * Generate user-specific cookie name using HMAC hash
     */
    public function getUserSpecificCookieName(int $userId): string
    {
        $uidHash = $this->generateUserIdHash($userId);
        return "remember_2fa_{$uidHash}";
    }

    /**
     * Generate HMAC-based hash of user ID for cookie naming
     */
    private function generateUserIdHash(int $userId): string
    {
        $key = config('app.key');
        return substr(hash_hmac('sha256', $userId, $key), 0, 16);
    }

    /**
     * Generate an encrypted remember 2FA cookie value
     */
    public function generateRemember2FACookieValue(int $userId): string
    {
        // Create payload with user ID, expiration, nonce, and version
        $payload = [
            'uid' => $userId,
            'exp' => now()->addDays(30)->getTimestamp() * 1000, // Unix epoch in milliseconds
            'nonce' => Str::random(16), // 128-bit random nonce
            'ver' => 1 // Version for future compatibility
        ];

        $jsonPayload = json_encode($payload);

        // Encrypt the payload using Laravel's Crypt
        return Crypt::encryptString($jsonPayload);
    }

    /**
     * Validate remember 2FA cookie value using decryption
     */
    public function validateRemember2FACookieValue(string $cookieValue, int $userId): ?array
    {
        try {
            // Decrypt the cookie value
            $decryptedPayload = Crypt::decryptString($cookieValue);
            $data = json_decode($decryptedPayload, true);

            if (!$data || !isset($data['uid'], $data['exp'], $data['nonce'], $data['ver'])) {
                Log::warning('Remember 2FA cookie missing required fields', [
                    'user_id' => $userId,
                    'data_keys' => $data ? array_keys($data) : 'null'
                ]);
                return null;
            }

            // Verify this cookie belongs to the current user
            if ($data['uid'] !== $userId) {
                Log::warning('Remember 2FA cookie user ID mismatch', [
                    'cookie_user_id' => $data['uid'],
                    'current_user_id' => $userId
                ]);
                return null;
            }

            // Check expiration (timestamp is in milliseconds)
            $expirationTimestamp = $data['exp'] / 1000; // Convert to seconds
            if (now()->getTimestamp() > $expirationTimestamp) {
                Log::info('Remember 2FA cookie expired', [
                    'user_id' => $userId,
                    'expiration_timestamp' => $expirationTimestamp,
                    'current_timestamp' => now()->getTimestamp()
                ]);
                return null;
            }

            // Verify version compatibility
            if ($data['ver'] !== 1) {
                Log::warning('Remember 2FA cookie version mismatch', [
                    'user_id' => $userId,
                    'cookie_version' => $data['ver'],
                    'expected_version' => 1
                ]);
                return null;
            }

            return $data;

        } catch (\Exception $e) {
            Log::warning('Failed to validate remember 2FA cookie', [
                'error' => $e->getMessage(),
                'user_id' => $userId,
                'cookie_value' => substr($cookieValue, 0, 50) . '...'
            ]);
            return null;
        }
    }

    /**
     * Get remember 2FA cookie configuration for a specific user
     */
    public function getRemember2FACookieConfig(int $userId): array
    {
        $config = config('security.remember_2fa');

        // Set user-specific cookie name
        $config['name'] = $this->getUserSpecificCookieName($userId);

        // Auto-detect domain and secure flag
        $config['domain'] = $this->detectCookieDomain();
        $config['secure'] = $this->shouldUseSecureCookies();

        // Set proper cookie flags for security
        $config['http_only'] = false; // Keep false for cross-platform compatibility
        $config['same_site'] = 'Lax';
        $config['expires_days'] = 30;

        return $config;
    }

    /**
     * Invalidate remember 2FA cookie for a specific user
     * This method provides the configuration needed to delete the cookie
     */
    public function getInvalidationConfig(int $userId): array
    {
        return [
            'name' => $this->getUserSpecificCookieName($userId),
            'domain' => $this->detectCookieDomain(),
            'path' => '/',
            'secure' => $this->shouldUseSecureCookies(),
            'same_site' => 'Lax'
        ];
    }



    /**
     * Auto-detect cookie domain based on environment
     */
    private function detectCookieDomain(): ?string
    {
        // In local development, don't set domain to allow cross-origin cookies
        if (app()->environment('local')) {
            return null;
        }

        // For production, you might want to set a specific domain
        // This can be configured via environment variables if needed
        return config('session.domain');
    }

    /**
     * Determine if secure cookies should be used
     */
    private function shouldUseSecureCookies(): bool
    {
        // Auto-detect based on environment
        if (app()->environment('local')) {
            return false; // Allow HTTP in local development
        }

        // Force HTTPS in production
        return true;
    }

    /**
     * Check if remember 2FA cookie is valid for a user
     */
    public function isRemember2FAValid(string $cookieValue, int $userId): bool
    {
        $validatedData = $this->validateRemember2FACookieValue($cookieValue, $userId);
        return $validatedData !== null;
    }

    /**
     * Log remember 2FA cookie usage for security monitoring
     */
    public function logRemember2FAUsage(int $userId, string $action, array $context = []): void
    {
        Log::info('Remember 2FA cookie action', array_merge([
            'user_id' => $userId,
            'action' => $action, // 'generated', 'validated', 'bypassed_2fa', 'expired', 'invalid', 'deleted'
            'timestamp' => now()->toISOString(),
            'cookie_name' => $this->getUserSpecificCookieName($userId)
        ], $context));
    }
}
