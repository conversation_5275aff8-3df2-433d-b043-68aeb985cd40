{"/(Auth)/login/page": "app/(Auth)/login/page.js", "/(Auth)/security-check/page": "app/(Auth)/security-check/page.js", "/(Auth)/signup/page": "app/(Auth)/signup/page.js", "/(Home)/blog/[detail]/page": "app/(Home)/blog/[detail]/page.js", "/(Home)/blog/page": "app/(Home)/blog/page.js", "/(Home)/category/page": "app/(Home)/category/page.js", "/(Home)/education/[detail]/page": "app/(Home)/education/[detail]/page.js", "/(Home)/education/page": "app/(Home)/education/page.js", "/(Home)/marketplace/[detail]/page": "app/(Home)/marketplace/[detail]/page.js", "/(Home)/marketplace/page": "app/(Home)/marketplace/page.js", "/(User)/account/(View)/followers/page": "app/(User)/account/(View)/followers/page.js", "/(User)/account/address/manage/page": "app/(User)/account/address/manage/page.js", "/(User)/account/details/page": "app/(User)/account/details/page.js", "/(User)/account/overview/page": "app/(User)/account/overview/page.js", "/(User)/account/security/page": "app/(User)/account/security/page.js", "/(User)/account/security/two-factor/page": "app/(User)/account/security/two-factor/page.js", "/(User)/dashboard/page": "app/(User)/dashboard/page.js", "/[slug]/page": "app/[slug]/page.js", "/_not-found/page": "app/_not-found/page.js", "/page": "app/page.js"}