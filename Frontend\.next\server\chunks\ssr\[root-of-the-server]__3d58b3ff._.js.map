{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28User%29/account/security/two-factor/TwoFactorComponent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(User)/account/security/two-factor/TwoFactorComponent.js <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(User)/account/security/two-factor/TwoFactorComponent.js <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoU,GACjW,kGACA", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28User%29/account/security/two-factor/TwoFactorComponent.js/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/app/(User)/account/security/two-factor/TwoFactorComponent.js from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/app/(User)/account/security/two-factor/TwoFactorComponent.js\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 50, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Jerrax/Frontend/src/app/%28User%29/account/security/two-factor/page.js"], "sourcesContent": ["import { Suspense } from \"react\";\r\nimport TwoFactorComponent from \"./TwoFactorComponent\";\r\nimport MetaHead from \"@/Seo/Meta/MetaHead\";\r\n\r\nexport default function TwoFactor() {\r\n    const metaArray = {\r\n        noindex: true,\r\n        title: \"Account Security | Two-Factor Setup | TradeReply\",\r\n        description:\r\n            \"Add an extra layer of security to your TradeReply account with Two-Factor Authentication (2FA). Enable or disable 2FA and manage trusted devices.\",\r\n        canonical_link: \"https://www.tradereply.com/account/security/two-factor\",\r\n        og_site_name: \"TradeReply\",\r\n        og_title: \"Account Security | Two-Factor Setup | TradeReply\",\r\n        og_description:\r\n            \"Add an extra layer of security to your TradeReply account with Two-Factor Authentication (2FA). Enable or disable 2FA and manage trusted devices.\",\r\n        og_url: \"https://www.tradereply.com/account/security/two-factor\",\r\n        og_type: \"website\",\r\n        og_image:\r\n            \"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\",\r\n        og_image_width: \"1200\",\r\n        og_image_height: \"630\",\r\n        og_locale: \"en_US\",\r\n        twitter_card: \"summary_large_image\",\r\n        twitter_title: \"Account Security | Two-Factor Setup | TradeReply\",\r\n        twitter_description:\r\n            \"Add an extra layer of security to your TradeReply account with Two-Factor Authentication (2FA). Enable or disable 2FA and manage trusted devices.\",\r\n        twitter_image:\r\n            \"https://cdn.tradereply.com/dev/misc/tradereply-trading-analytics-og.png\",\r\n        twitter_site: \"@JoinTradeReply\",\r\n        robots: \"noindex, nofollow\",\r\n    };\r\n\r\n    return (\r\n        <Suspense fallback={<div>Loading...</div>}>\r\n            <MetaHead props={metaArray} />\r\n            <TwoFactorComponent />\r\n        </Suspense>\r\n    );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS;IACpB,MAAM,YAAY;QACd,SAAS;QACT,OAAO;QACP,aACI;QACJ,gBAAgB;QAChB,cAAc;QACd,UAAU;QACV,gBACI;QACJ,QAAQ;QACR,SAAS;QACT,UACI;QACJ,gBAAgB;QAChB,iBAAiB;QACjB,WAAW;QACX,cAAc;QACd,eAAe;QACf,qBACI;QACJ,eACI;QACJ,cAAc;QACd,QAAQ;IACZ;IAEA,qBACI,8OAAC,qMAAA,CAAA,WAAQ;QAAC,wBAAU,8OAAC;sBAAI;;;;;;;0BACrB,8OAAC,8HAAA,CAAA,UAAQ;gBAAC,OAAO;;;;;;0BACjB,8OAAC,oLAAA,CAAA,UAAkB;;;;;;;;;;;AAG/B", "debugId": null}}]}