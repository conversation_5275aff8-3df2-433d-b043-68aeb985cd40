{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/Home/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/Home/Category.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.categorySec {\r\n  .container {\r\n    max-width: 1080px;\r\n  }\r\n\r\n  &_heading {\r\n\r\n    h1 {\r\n      font-size: 3rem !important;\r\n      font-weight: 800;\r\n    }\r\n\r\n    p {\r\n      font-size: 1.25rem;\r\n      font-weight: 600;\r\n      line-height: 28px;\r\n      letter-spacing: -0.1px;\r\n      margin: 30px 0;\r\n\r\n      @media (max-width: 991px) {\r\n        font-size: 1rem;\r\n        line-height: 22px;\r\n        margin: 20px 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_search {\r\n    .commonSearch {\r\n      margin: 0 auto;\r\n      max-width: 400px;\r\n\r\n      .form-control {\r\n        width: 100%;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_fliters {\r\n    padding: 30px 0 50px;\r\n\r\n    @media (max-width: 991px) {\r\n      padding: 20px 0 30px;\r\n    }\r\n\r\n    @media (max-width: 767px) {\r\n      padding: 20px 0 10px;\r\n    }\r\n\r\n    &_inner {\r\n      display: flex;\r\n      align-items: center;\r\n      margin-bottom: 30px;\r\n      padding: 0 1.5rem;\r\n\r\n      @media (max-width: 991px) {\r\n        padding: 0 1rem;\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        margin-bottom: 20px;\r\n      }\r\n\r\n      .slider {\r\n        display: flex;\r\n        overflow-x: auto;\r\n        scrollbar-width: none;\r\n      }\r\n\r\n      .slider::-webkit-scrollbar {\r\n        display: none;\r\n        /* Hide scrollbar for Chrome, Safari, and Edge */\r\n      }\r\n\r\n      .scroll-btn {\r\n        background-color: var.$baseclr;\r\n        color: white;\r\n        border: none;\r\n        padding: 0;\r\n        cursor: pointer;\r\n        font-size: 1.2rem;\r\n        min-width: 30px;\r\n        min-height: 30px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        border-radius: 10rem;\r\n        position: relative;\r\n\r\n        &.left {\r\n          left: -30px;\r\n\r\n          @media (max-width: 767px) {\r\n            left: -14px;\r\n          }\r\n\r\n          img {\r\n            transform: rotate(180deg);\r\n          }\r\n        }\r\n\r\n        &.right {\r\n          right: -30px;\r\n\r\n          @media (max-width: 767px) {\r\n            right: -14px;\r\n          }\r\n        }\r\n\r\n        &:hover {\r\n          background-color: var.$baseclr;\r\n        }\r\n\r\n        &.disabled,\r\n        &:disabled {\r\n          background-color: #414c60;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_boxbutton {\r\n      width: auto;\r\n      min-height: 35px;\r\n      border-radius: 5px;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: center;\r\n      margin-right: 10px;\r\n      color: var.$white !important;\r\n      font-size: 1rem;\r\n      font-weight: 600;\r\n      line-height: 1.5rem;\r\n      letter-spacing: -0.1px;\r\n      background-color: var.$bluelightclr;\r\n      border: 0;\r\n      transition: all ease-in-out 0.3s;\r\n      padding: 5px 10px;\r\n      cursor: pointer;\r\n\r\n      @media (max-width: 767px) {\r\n        font-size: 0.875rem;\r\n      }\r\n\r\n      .active,\r\n      a {\r\n        color: white !important;\r\n      }\r\n\r\n      &:last-child {\r\n        margin-right: 0;\r\n      }\r\n\r\n      &:hover,\r\n      &.active,\r\n      .selected {\r\n        background-color: var.$baseclr;\r\n      }\r\n    }\r\n\r\n    &_boxadd {\r\n      padding: 5px 15px;\r\n      background-color: var.$baseclr;\r\n      border-radius: 15px;\r\n      display: inline-flex;\r\n      align-items: center;\r\n\r\n      h6 {\r\n        color: var.$white;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_pagination {\r\n    display: flex;\r\n    justify-content: flex-end;\r\n  }\r\n\r\n  &_term {\r\n    &_content {\r\n      p {\r\n        color: var.$lightgreyclr;\r\n        font-size: 1.125rem;\r\n        font-weight: 500;\r\n        line-height: 28px;\r\n        letter-spacing: -0.1px;\r\n      }\r\n    }\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AAAE;;;;AAME;;;;;AAKA;;;;;;;;AAOE;EAPF;;;;;;;AAgBA;;;;;AAIE;;;;AAMJ;;;;AAGE;EAHF;;;;;AAOE;EAPF;;;;;AAWE;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;AAcE;;;;;;AAMA;;;;AAKA;;;;;;;;;;;;;;;;AAeE;;;;AAGE;EAHF;;;;;AAOE;;;;AAKF;;;;AAGE;EAHF;;;;;AAQA;;;;AAIA;;;;AAOJ;;;;;;;;;;;;;;;;;;;;AAmBE;EAnBF;;;;;AAuBE;;;;AAKA;;;;AAIA;;;;AAOF;;;;;;;;AAOE;;;;AAMJ;;;;;AAOI"}}]}