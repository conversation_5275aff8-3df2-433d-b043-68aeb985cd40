{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/CommonButton.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.btn-style,\r\n.btn-primary {\r\n  min-height: 66px;\r\n  display: inline-flex;\r\n  justify-content: center;\r\n  align-items: center;\r\n  text-align: center;\r\n  border-radius: 10rem;\r\n  padding: 0.5rem 1.5rem;\r\n  font-size: 1.25rem;\r\n  font-weight: 600;\r\n  background-color: var.$baseclr;\r\n  border: 0;\r\n  text-transform: capitalize;\r\n  transition: all ease-in-out 0.3s;\r\n  min-width: 150px;\r\n  color: var.$white;\r\n  \r\n  span{\r\n    line-height: 1;\r\n  }\r\n\r\n  @media (max-width: 1599px) {\r\n    min-height: 66px;\r\n  }\r\n\r\n  @media (max-width: 1199px) {\r\n    min-height: 56px;\r\n    font-size: 1.125rem;\r\n    font-weight: 500;\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    min-height: 46px;\r\n    font-size: 1rem;\r\n  }\r\n\r\n  &:hover {\r\n    background-color: var.$baseclrhover;\r\n    color: var.$white;\r\n  }\r\n\r\n  &.transparent {\r\n    background-color: transparent;\r\n    border: none;\r\n  }\r\n\r\n  &.white-btn {\r\n    background: var.$white;\r\n    color: var.$black;\r\n\r\n    &:hover {\r\n      background: var.$baseclr;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.yellow-btn {\r\n    background-color: var.$yellow;\r\n    color: var.$white;\r\n\r\n    &:hover {\r\n      background-color: var.$yellowBtnHover;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.gray-btn {\r\n    background-color: var.$grayBtn !important;\r\n    color: var.$white;\r\n\r\n    &:hover {\r\n      background-color: var.$grayBtnHover;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.gradient-btn {\r\n    background: linear-gradient(75deg, #00aeef, #1f5aff 50.31%, #da00ff);\r\n    color: var.$white;\r\n\r\n    &:hover {\r\n      background: linear-gradient(75deg, #0043ff, #1f5aff 50.31%, #da00ff);\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.green-btn {\r\n    background-color: var.$green;\r\n    color: var.$white;\r\n\r\n    &:hover {\r\n      background-color: var.$greenbtnhover;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.red-btn {\r\n    background-color: var.$redlightclr;\r\n    color: var.$white;\r\n\r\n    &:hover {\r\n      background-color: var.$redbghover;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  &.border-btn {\r\n    background: transparent;\r\n    color: var.$white;\r\n    border: 1px solid var.$baseclr;\r\n\r\n    &:hover {\r\n      background: var.$baseclr;\r\n      color: var.$white;\r\n    }\r\n  }\r\n\r\n  .onlyIcon {\r\n    margin-right: 15px;\r\n    display: inline-flex;\r\n  }\r\n\r\n  &:disabled,\r\n  &.disabled {\r\n    background: var.$textclr;\r\n    color: var.$white;\r\n    cursor: not-allowed;\r\n    opacity: 1;\r\n  }\r\n}\r\n\r\n:disabled,\r\n.disabled {\r\n  background-color: #414c60;\r\n  color: var.$white;\r\n  cursor: not-allowed;\r\n  opacity: 1;\r\n}\r\n\r\n.white20 {\r\n  background-color: #ffffff1f;\r\n  width: 100%;\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;;;;;;;;;;;;;;;AAkBE;;;;AAIA;EAtBF;;;;;AA0BE;EA1BF;;;;;;;AAgCE;EAhCF;;;;;;AAqCE;;;;;AAKA;;;;;AAKA;;;;;AAIE;;;;;AAMF;;;;;AAIE;;;;;AAMF;;;;;AAIE;;;;;AAMF;;;;;AAIE;;;;;AAMF;;;;;AAIE;;;;;AAMF;;;;;AAIE;;;;;AAMF;;;;;;AAKE;;;;;AAMF;;;;;AAKA;;;;;;;AASF;;;;;;;AAQA", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/Header.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\" as *;\r\n\r\nheader {\r\n  position: sticky;\r\n  top: 0;\r\n  left: 0;\r\n  z-index: 9998;\r\n}\r\n\r\n.home-page {\r\n  .siteHeader {\r\n    background-color: $black !important;\r\n    border-bottom: 0;\r\n\r\n    .navMenu {\r\n      .common_dropdown {\r\n        &.dropdown {\r\n          .dropdown-toggle {\r\n\r\n            &.show,\r\n            &:hover {\r\n              @media (min-width: 1200px) {\r\n                background-color: $clr2A2E39 !important;\r\n                color: $white;\r\n\r\n                &::after {\r\n                  transform: rotate(180deg);\r\n                }\r\n              }\r\n            }\r\n          }\r\n\r\n          &.show {\r\n            .dropdown-toggle {\r\n              &::after {\r\n                transform: rotate(180deg);\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .dropdown-menu {\r\n          @media (min-width: 1200px) {\r\n            background-color: $clr1E222D !important;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .common_dropdown {\r\n      &.dropdown {\r\n        .dropdown-menu {\r\n          .dropdown-item {\r\n\r\n            &:hover,\r\n            &.active,\r\n            &:focus {\r\n              background-color: $clr2A2E39 !important;\r\n            }\r\n\r\n            &.white_stroke_icon {\r\n              font-weight: 700;\r\n              background: $gradientblackbg !important;\r\n              color: $clr00b9ff;\r\n              transition: none;\r\n\r\n              svg {\r\n                path {\r\n                  fill: $clr00b9ff;\r\n                }\r\n              }\r\n\r\n              &:hover,\r\n              &.active {\r\n                background: $clr2A2E39 !important;\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .navbar {\r\n      &-collapse {\r\n        .nav-link {\r\n          @media (max-width: 1199px) {\r\n            &.white_stroke_icon {\r\n              font-weight: 700;\r\n              background: linear-gradient(to right,\r\n                  #000000,\r\n                  #2d2d2d) !important;\r\n              color: #00aeef;\r\n              transition: none;\r\n\r\n              svg {\r\n                path {\r\n                  fill: $clr00b9ff;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .navbar-collapse {\r\n      @media (max-width: 1199px) {\r\n        background-color: rgba(0, 0, 0, 0.9) !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  @media (width >=1200px) {\r\n\r\n    .siteHeader .navbar-collapse .nav-link:hover,\r\n    .siteHeader .navbar-collapse .nav-link.active,\r\n    .siteHeader .navbar-collapse .nav-link:focus {\r\n      background-color: #2a2e39 !important;\r\n      color: #fff;\r\n    }\r\n\r\n    .languageDropdown {\r\n      width: 64px;\r\n\r\n      @media (max-width: 1199px) {\r\n        width: 100%;\r\n      }\r\n\r\n      .common_dropdown {\r\n        @media (max-width: 1199px) {\r\n          width: 100%;\r\n        }\r\n\r\n        .nav-link:hover,\r\n        .nav-link.active,\r\n        .nav-link:focus {\r\n          color: #fff;\r\n          background-color: $clr2A2E39 !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover,\r\n    .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {\r\n      background: #2a2e39 !important;\r\n    }\r\n  }\r\n}\r\n\r\n.siteHeader {\r\n  height: 80px;\r\n  padding: 1rem 0;\r\n  display: flex;\r\n  align-items: center;\r\n  justify-content: center;\r\n  backdrop-filter: blur(10px);\r\n  background-color: $clr031940;\r\n  border-bottom: 1px solid $clr064197;\r\n\r\n  .btn-style {\r\n    min-height: 56px;\r\n    min-width: 169px;\r\n\r\n    @media (max-width: 1199px) {\r\n      min-height: 40px;\r\n      min-width: 120px;\r\n      padding: 8px 1rem;\r\n      font-size: 14px;\r\n    }\r\n\r\n    @media (max-width: 575px) {\r\n      min-height: 34px;\r\n      min-width: 80px;\r\n      font-size: 14px;\r\n      ;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 1199px) {\r\n    z-index: 9999;\r\n    backdrop-filter: none;\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    padding: 0.625rem 0;\r\n  }\r\n\r\n  .navbar {\r\n    padding: 0;\r\n    width: 100%;\r\n\r\n    .brandLogo {\r\n      img {\r\n        max-width: 190px;\r\n        width: 100%;\r\n\r\n        @media (max-width: 767px) {\r\n          max-width: 150px;\r\n          margin-right: 0rem;\r\n        }\r\n\r\n        @media (max-width: 360px) {\r\n          max-width: 120px;\r\n          margin-right: 0rem;\r\n        }\r\n      }\r\n    }\r\n\r\n    &-collapse {\r\n      height: auto !important;\r\n\r\n      .nav-link {\r\n        font-size: 1.25rem;\r\n        font-weight: 400;\r\n        background-color: transparent;\r\n        display: flex;\r\n        align-items: center;\r\n        white-space: nowrap;\r\n        padding: 0.5rem 1.5rem;\r\n        color: $white;\r\n\r\n        &:hover,\r\n        &.active,\r\n        &:focus {\r\n          color: $baseclr;\r\n\r\n          @media (min-width: 1200px) {\r\n            background-color: $clr283f67 !important;\r\n            color: $white;\r\n          }\r\n        }\r\n\r\n        @media (min-width: 1200px) {\r\n          margin: 0 3px;\r\n        }\r\n\r\n        @media (max-width: 1199px) {\r\n          padding: 1.25rem 0rem;\r\n          border-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n          font-size: 1.125rem;\r\n\r\n          img {\r\n            width: 22px;\r\n          }\r\n\r\n          &.white_stroke_icon {\r\n            font-weight: 700;\r\n            background: linear-gradient(to right, #031940, #283f67);\r\n            color: #00aeef;\r\n            transition: none;\r\n\r\n            svg {\r\n              path {\r\n                fill: $clr00b9ff;\r\n              }\r\n            }\r\n\r\n            // &:hover,\r\n            // &.active {\r\n            //     background: $clr283f67;\r\n            // }\r\n          }\r\n        }\r\n      }\r\n\r\n      @media (max-width: 1199px) {\r\n        position: fixed;\r\n        left: -350px;\r\n        top: 0px;\r\n        background-color: rgba(3, 25, 64, 0.9);\r\n        backdrop-filter: blur(5px);\r\n        width: 350px;\r\n        padding: 1.25rem 1rem;\r\n        display: block;\r\n        transition: all ease-in-out 0.2s;\r\n        height: 100vh !important;\r\n        z-index: 9999;\r\n        padding: 0;\r\n\r\n        a {\r\n          display: flex;\r\n          justify-content: flex-start;\r\n          text-align: left;\r\n        }\r\n\r\n        &.show {\r\n          left: 0;\r\n          height: 100vh;\r\n        }\r\n\r\n        .navMenu {\r\n          padding: 20px;\r\n          max-height: calc(100vh - 84px);\r\n          max-height: calc(100dvh - 84px);\r\n          overflow-y: auto;\r\n        }\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        left: -100%;\r\n        width: 100%;\r\n      }\r\n\r\n      .navMenu {\r\n        .common_dropdown {\r\n          &.dropdown {\r\n            .dropdown-toggle {\r\n              padding: 0.5rem 1.5rem !important;\r\n              border-radius: 0;\r\n\r\n              @media (max-width: 1199px) {\r\n                padding: 1.25rem 0rem !important;\r\n                border-bottom: 1px solid rgba(255, 255, 255, 0.2);\r\n                width: 100%;\r\n              }\r\n\r\n              &::after {\r\n                display: block;\r\n                background-image: url(\"https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg\");\r\n                background-repeat: no-repeat;\r\n                background-size: 1.15rem;\r\n                background-position: center;\r\n                width: 1.15rem;\r\n                height: 1.15rem;\r\n                border: 0;\r\n                transition: all ease-in-out 0.3s;\r\n                margin-left: 1rem;\r\n\r\n                @media (max-width: 1199px) {\r\n                  margin-left: 0;\r\n                  position: absolute;\r\n                  right: 0;\r\n                }\r\n              }\r\n\r\n              &.show,\r\n              &:hover {\r\n                @media (min-width: 1200px) {\r\n                  background-color: $clr283f67;\r\n                  color: $white;\r\n\r\n                  &::after {\r\n                    transform: rotate(180deg);\r\n                  }\r\n                }\r\n              }\r\n            }\r\n\r\n            &.show {\r\n              .dropdown-toggle {\r\n                &::after {\r\n                  transform: rotate(180deg);\r\n                }\r\n              }\r\n            }\r\n\r\n            .dropdown-menu {\r\n              @media screen and (max-width: 1199px) {\r\n                position: static;\r\n                border: 0;\r\n                background-color: transparent;\r\n                padding: 0;\r\n              }\r\n\r\n              .nav-link {\r\n                padding: 0.875rem 1.5rem;\r\n                align-items: start;\r\n                font-weight: 400 !important;\r\n\r\n                @media screen and (max-width: 1199px) {\r\n                  padding: 0.875rem 1rem;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .navbar-toggler {\r\n      background-color: transparent;\r\n      margin-left: 0;\r\n      padding: 0;\r\n      position: relative;\r\n      width: 24px;\r\n      height: 18px;\r\n\r\n      &:focus {\r\n        box-shadow: none;\r\n      }\r\n\r\n      @media (max-width: 1199px) {\r\n        margin-right: 13px;\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        margin-right: 13px;\r\n      }\r\n\r\n      &::after {\r\n        content: \"\";\r\n        position: absolute;\r\n        bottom: 0;\r\n        left: 0;\r\n        width: 24px;\r\n        background-color: $white;\r\n        height: 2px;\r\n        transition: all ease-in-out 0.3s;\r\n      }\r\n\r\n      &::before {\r\n        content: \"\";\r\n        position: absolute;\r\n        top: 0;\r\n        left: 0;\r\n        width: 24px;\r\n        background-color: $white;\r\n        height: 2px;\r\n        transition: all ease-in-out 0.3s;\r\n      }\r\n\r\n      .navbar-toggler-icon {\r\n        background-image: none;\r\n        height: 2px;\r\n        background-color: $white;\r\n        width: 24px;\r\n        transition: all ease-in-out 0.3s;\r\n        display: flex;\r\n      }\r\n    }\r\n\r\n    .common_dropdown {\r\n      &.dropdown {\r\n        .dropdown-toggle {\r\n          padding: 0.5rem 0.2rem !important;\r\n          color: $white;\r\n          border: 0;\r\n          border-radius: 0.625rem;\r\n          font-size: 1.25rem;\r\n          padding: 0;\r\n          display: flex;\r\n          align-items: center;\r\n\r\n          @media (max-width: 991px) {\r\n            font-size: 1.125rem;\r\n          }\r\n\r\n          &::after {\r\n            display: none;\r\n          }\r\n\r\n          &.show {\r\n            svg {\r\n              path {\r\n                fill: $baseclr;\r\n              }\r\n            }\r\n          }\r\n        }\r\n\r\n        .dropdown-menu {\r\n          border-radius: 0.625rem;\r\n          border: 1px solid rgba(255, 255, 255, 0.3);\r\n          min-width: 200px;\r\n          position: absolute;\r\n          top: 45px;\r\n\r\n          @media screen and (max-width: 1199px) {\r\n            position: static;\r\n            padding: 0;\r\n            min-width: 100%;\r\n          }\r\n\r\n          .dropdown-item {\r\n            font-size: 1.125rem;\r\n            font-weight: 600;\r\n            padding: 0.625rem 1rem;\r\n            color: $white;\r\n\r\n            @media (max-width: 991px) {\r\n              font-size: 1rem;\r\n            }\r\n\r\n            svg,\r\n            img {\r\n              margin-right: 10px;\r\n            }\r\n\r\n            &:hover,\r\n            &.active,\r\n            &:focus {\r\n              background: $clr283f67;\r\n            }\r\n\r\n            &.white_stroke_icon {\r\n              font-weight: 700;\r\n              background: $gradientbluebg;\r\n              color: $clr00b9ff;\r\n              transition: none;\r\n\r\n              svg {\r\n                path {\r\n                  fill: $clr00b9ff;\r\n                }\r\n              }\r\n\r\n              &:hover,\r\n              &.active {\r\n                background: $clr283f67 !important;\r\n              }\r\n            }\r\n          }\r\n\r\n          &.show {\r\n\r\n            svg,\r\n            img {\r\n              width: 18px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    @media screen and (max-width: 1199px) {\r\n      .openmenuSidebar {\r\n        border-bottom: 1px solid rgba(255, 255, 255, 0.5);\r\n        padding: 30px 15px;\r\n\r\n        .brandLogo {\r\n          padding: 0;\r\n\r\n          img {\r\n            max-width: 150px;\r\n          }\r\n        }\r\n\r\n        .navbar-toggler {\r\n          position: absolute;\r\n          right: 15px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  &.openmenu {\r\n    .navbar {\r\n      .navbar-toggler {\r\n        &::after {\r\n          transform: rotate(45deg) translate(-5px, -5px);\r\n          background-color: $white;\r\n        }\r\n\r\n        &::before {\r\n          transform: rotate(-45deg) translate(-5px, 5px);\r\n          background-color: $white;\r\n        }\r\n\r\n        .navbar-toggler-icon {\r\n          opacity: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .user_icon {\r\n    img {\r\n      width: 26px;\r\n      height: 26px;\r\n    }\r\n\r\n  }\r\n\r\n  .sidebar_backdrop {\r\n    @media screen and (max-width: 767px) {\r\n      display: none;\r\n    }\r\n  }\r\n}\r\n\r\n.languageDropdown {\r\n  width: 64px;\r\n\r\n  @media (max-width: 1199px) {\r\n    width: 100%;\r\n  }\r\n\r\n  .common_dropdown {\r\n    @media (max-width: 1199px) {\r\n      width: 100%;\r\n    }\r\n\r\n    .nav-link:hover,\r\n    .nav-link.active,\r\n    .nav-link:focus {\r\n      color: #fff;\r\n      background-color: #283f67 !important;\r\n    }\r\n\r\n    &.dropdown {\r\n      .dropdown-toggle {\r\n        color: $white;\r\n        border: 0;\r\n        border-radius: 0 !important;\r\n        font-size: 1.25rem;\r\n        padding: 0;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        @media (max-width: 991px) {\r\n          font-size: 1rem;\r\n        }\r\n\r\n        svg {\r\n          margin-right: 10px;\r\n        }\r\n\r\n        &:focus,\r\n        &:hover {\r\n          background-color: transparent !important;\r\n        }\r\n\r\n        @media (max-width: 1199px) {\r\n          width: 100%;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .globalIcon .icon {\r\n    transition: opacity 0.3s ease;\r\n  }\r\n\r\n  .globalIcon .blue {\r\n    display: none;\r\n  }\r\n\r\n  .nav-item:hover .globalIcon .black,\r\n  .nav-item.show .globalIcon .black {\r\n    display: none;\r\n  }\r\n\r\n  .nav-item:hover .globalIcon .blue,\r\n  .nav-item.show .globalIcon .blue {\r\n    display: block;\r\n  }\r\n}\r\n\r\n.userDropdown {\r\n  &.common_dropdown {\r\n    &.dropdown {\r\n      .dropdown-toggle {\r\n        // width: 40px;\r\n        // height: 40px;\r\n        // justify-content: center;\r\n        // border-radius: 0 !important;\r\n\r\n        .user_name {\r\n          display: none;\r\n\r\n          @media screen and (max-width: 1199px) {\r\n            display: block;\r\n            padding-left: 10px;\r\n            font-size: 18px;\r\n\r\n            svg {\r\n              width: 26px;\r\n              height: 26px;\r\n            }\r\n          }\r\n        }\r\n\r\n        @media screen and (max-width: 1199px) {\r\n          border-bottom: 0 !important;\r\n        }\r\n\r\n        &:hover {\r\n          background-color: transparent !important;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.brandLogo {\r\n  @media (max-width: 1199px) {\r\n    display: flex;\r\n  }\r\n\r\n  img {\r\n    @media (max-width: 1199px) {\r\n      max-width: 150px;\r\n    }\r\n\r\n    @media (max-width: 767px) {\r\n      max-width: 110px;\r\n    }\r\n\r\n    @media (max-width: 359px) {\r\n      max-width: 100px;\r\n    }\r\n  }\r\n}\r\n\r\n.sidebar_backdrop {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100%;\r\n  height: 100vh;\r\n  // backdrop-filter: blur(1px);\r\n  z-index: 1000;\r\n  background-color: rgba(0, 0, 0, 0.2);\r\n  transition: all ease-in-out 0.2s;\r\n}\r\n\r\n.image_color_to_white {\r\n  filter: brightness(0) invert(1);\r\n}\r\n\r\n.nav-link {\r\n\r\n  &:hover,\r\n  &.active,\r\n  &:focus {\r\n    @media (min-width: 1200px) {\r\n      background-color: $clr2A2E39 !important;\r\n      color: $white;\r\n    }\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;;;;;AAQE;;;;;AAWY;EAFF;;;;;EAMI;;;;;AASF;;;;AAQJ;EADF;;;;;AAaI;;;;AAMA;;;;;;;AAOI;;;;AAKF;;;;AAaJ;EACE;;;;;;;EASI;;;;EA3CJ;;;;;AAuDJ;EAAA;;;;;EAUF;;;;;AAIF;EAGE;;;;;AAKE;EADF;;;;;EAKE;;;;;AASJ;;;;;;;;;;;;AAaF;;;;;AAEA;EAEA;;;;;;;;AAAA;EAKI;;;;;;;AAMF;EAXF;;;;;;;AAcI;EAKJ;;;;;AAEE;;;;;AAGF;;;;;AAKE;EAIE;;;;;;AAIE;EAJF;;;;;;AASE;;;;AACE;;;;;;;;;;;AAaF;;;;AAMA;EAAA;;;;;EAAA;;;;;AAMI;EAKJ;;;;;;EArBF;;;;EA2BI;;;;;;;EAOA;;;;EAGE;;;;;;;;;;;;;;;EAsBJ;;;;;;EAKA;;;;;EAIA;;;;;;;;AAWA;EAAA;;;;;;AAEE;;;;;AAcE;EAAA;;;;;;;AAAA;;;;;;;;;;;;;AAcI;EACA;;;;;;;AALF;EAAA;;;;;EAmBA;;;;;AAMI;;;;AACE;EAQJ;;;;;;;;AAMJ;;;;;;AAQE;EAAA;;;;;AAKE;;;;;;;;;AAUZ;;;;AACE;EAEA;;;;;AAKA;EAAA;;;;;AARF;;;;;;;;;;;AAoBE;;;;;;;;;;;AAWA;;;;;;;;;AAWA;;;;;;;;;;;AAaI;EAAA;;;;;AAMA;;;;AAPF;;;;AAcE;;;;;;;;AAaF;EACE;;;;;;;AAMA;;;;;;;AAMA;EAAA;;;;;AAGE;;;;AAOA;;;;AAKA;;;;;;;AAMA;;;;AAIE;;;;AAQA;;;;AASF;EAAA;;;;;EASR;;;;EACE;;;;EAEE;;;;;;AAKE;;;;;AAOA;;;;;AAYA;;;;AAIA;;;;;AACA;EAGF;;;;;AAQJ;;;;AACE;EAOF;;;;;AAMJ;;;;;AAYI;;;;;;;;;;AAQE;EAAA;;;;;AAIE;;;;AAJF;;;;AAiBE;EAAA;;;;;AAAA;;;;AAKA;;;;AAWN;;;;AAIA;;;;AAKA;EAAA;;;;;;EAgBQ;;;;;EADF;;;;;AAQI;;;;AAOJ;EArBF;;;;EAAA;;;;;AAsBI;EAGF;;;;;AAAA;EACE;;;;;AAQR;;;;;;;;;;;AAIA;;;;AAAA;EAeF", "debugId": null}}, {"offset": {"line": 800, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/Footer.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.site_footer {\r\n  background-color: var.$black;\r\n\r\n  &_inner {\r\n    padding: 70px 0;\r\n\r\n    @media screen and (max-width: 991px) {\r\n      padding: 40px 0;\r\n    }\r\n  }\r\n\r\n  &_logo {\r\n    img {\r\n      width: 200px;\r\n    }\r\n  }\r\n\r\n  &_content {\r\n    p {\r\n      color: rgba(255, 255, 255, 0.65);\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      line-height: 26px;\r\n      letter-spacing: -0.10000000149011612px;\r\n      margin-top: 20px;\r\n\r\n      @media screen and (max-width: 991px) {\r\n        font-size: 16px;\r\n      }\r\n    }\r\n  }\r\n\r\n  &_links {\r\n    @media screen and (max-width: 767px) {\r\n      margin-top: 20px;\r\n    }\r\n\r\n    h4 {\r\n      color: var.$clrc5c5d5;\r\n      margin-bottom: 1.25rem;\r\n      font-size: 1.65rem;\r\n      line-height: 35px;\r\n      font-weight: 600;\r\n\r\n      @media screen and (max-width: 991px) {\r\n        font-size: 18px;\r\n      }\r\n    }\r\n\r\n    ul {\r\n      li {\r\n        a {\r\n          font-size: 20px;\r\n          font-weight: 600;\r\n          line-height: 24.5px;\r\n          letter-spacing: -0.10000000149011612px;\r\n          color: var.$white;\r\n          transition: all ease-in-out 0.3s;\r\n          padding-bottom: 10px;\r\n\r\n          @media screen and (max-width: 991px) {\r\n            font-size: 16px;\r\n          }\r\n\r\n          &:hover,\r\n          &.active {\r\n            color: var.$baseclr;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  &_copyright {\r\n    padding: 1.25rem 0;\r\n    border-top: 1px solid var.$white;\r\n\r\n    p {\r\n      text-align: center;\r\n      font-size: 18px;\r\n      font-weight: 600;\r\n      line-height: 26px;\r\n      letter-spacing: -0.10000000149011612px;\r\n\r\n      @media screen and (max-width: 991px) {\r\n        font-size: 16px;\r\n      }\r\n    }\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;AAGE;;;;AAGE;EAHF;;;;;AASE;;;;AAMA;;;;;;;;;AAQE;EARF;;;;;AAeA;EADF;;;;;AAKE;;;;;;;;AAOE;EAPF;;;;;AAcI;;;;;;;;;;AASE;EATF;;;;;AAaE;;;;AASR;;;;;AAIE;;;;;;;;AAOE;EAPF", "debugId": null}}, {"offset": {"line": 897, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/account/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/account/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/account/AccountSidebar.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.Account_sidebar {\r\n  position: sticky;\r\n  top: 88px;\r\n  left: 0;\r\n  background-color: var.$clr031940;\r\n  padding: 1.875rem 1.5rem 1rem;\r\n  height: calc(100vh - 88px);\r\n  overflow-y: auto;\r\n  width: 355px;\r\n  transition: all ease-in-out 0.3s;\r\n  z-index: 999;\r\n\r\n  @media (max-width: 1199px) {\r\n    left: -325px;\r\n    width: 325px;\r\n    height: 100vh;\r\n    position: fixed;\r\n    top: 0;\r\n    padding: 6rem 1.5rem 1.875rem;\r\n    z-index: 9999;\r\n    background-color: rgba(3, 25, 64, 0.9);\r\n    backdrop-filter: blur(5px);\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    left: -100%;\r\n    width: 100%;\r\n  }\r\n\r\n  &.opensidebar {\r\n    @media (max-width: 1199px) {\r\n      left: 0;\r\n    }\r\n  }\r\n\r\n  &_head {\r\n    @media (max-width: 1199px) {\r\n      position: absolute;\r\n      left: 50%;\r\n      transform: translateX(-50%);\r\n      top: 22px;\r\n      width: 100%;\r\n      text-align: center;\r\n\r\n      &::after {\r\n        content: \"\";\r\n        position: absolute;\r\n        right: 0;\r\n        bottom: -20px;\r\n        width: 100%;\r\n        height: 1px;\r\n        background-color: var.$borderclr;\r\n      }\r\n\r\n      .headLogo {\r\n        img {\r\n          width: 150px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  ul {\r\n    padding: 0;\r\n    margin: 0;\r\n    list-style: none;\r\n\r\n    li {\r\n      margin-bottom: 10px;\r\n\r\n      &:last-child {\r\n        margin-bottom: 0;\r\n      }\r\n\r\n      a {\r\n        color: var.$white;\r\n        font-size: 1.125rem;\r\n        font-weight: 600;\r\n        line-height: 24.5px;\r\n        letter-spacing: -0.10000000149011612px;\r\n        text-align: left;\r\n        padding: 15px 1rem;\r\n        border-left: 3px solid transparent;\r\n        position: relative;\r\n        z-index: 1;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        &::after {\r\n          content: \"\";\r\n          background: linear-gradient(\r\n            90deg,\r\n            rgba(4, 73, 140, 0.5) 0%,\r\n            rgba(4, 73, 140, 0) 100%\r\n          );\r\n          position: absolute;\r\n          bottom: 0;\r\n          left: 0;\r\n          width: 0;\r\n          height: 100%;\r\n          z-index: -1;\r\n          transition: all ease-in-out 0.3s;\r\n        }\r\n\r\n        span {\r\n          margin-right: 15px;\r\n\r\n          svg {\r\n            width: 20px;\r\n          }\r\n        }\r\n\r\n        &:hover,\r\n        &.active {\r\n          border-color: var.$baseclr;\r\n\r\n          &::after {\r\n            width: 100%;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  &_bottom_link {\r\n    border-top: 1px solid var.$clr04498C;\r\n    padding-top: 1.25rem;\r\n    margin-top: 1.25rem;\r\n\r\n    ul {\r\n      li {\r\n        a {\r\n          svg {\r\n            margin-left: 1rem;\r\n            transition: all ease-in-out 0.3s;\r\n          }\r\n\r\n          &:hover {\r\n            svg {\r\n              margin-left: 1.5rem;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .filter_toggle {\r\n    display: none;\r\n\r\n    @media (max-width: 1199px) {\r\n      position: absolute;\r\n      top: 0;\r\n      display: block;\r\n      right: 20px;\r\n      left: auto;\r\n    }\r\n  }\r\n}\r\n.Account_sidebar::-webkit-scrollbar{\r\n  width: 5px;\r\n  height: 4px;\r\n  border-radius: 1rem;\r\n}\r\n\r\n.Account_sidebar::-webkit-scrollbar-thumb {\r\n  background-color: #0557a3;\r\n  border-radius: 1rem;\r\n}\r\n.filter_toggle {\r\n  display: none;\r\n\r\n  @media (max-width: 1199px) {\r\n    position: absolute;\r\n    top: 51px;\r\n    left: 20px;\r\n    display: block;\r\n  }\r\n\r\n  &_btn {\r\n    background-color: transparent;\r\n    margin-left: 1.25rem;\r\n    padding: 0;\r\n    position: relative;\r\n    width: 26px;\r\n    height: 22px;\r\n    border: 0;\r\n\r\n    &:focus {\r\n      box-shadow: none;\r\n      border: 0;\r\n      outline: 0;\r\n    }\r\n\r\n    @media (max-width: 1199px) {\r\n      margin-left: 0;\r\n    }\r\n\r\n    @media (max-width: 767px) {\r\n      margin-left: 0;\r\n    }\r\n\r\n    &::after {\r\n      content: \"\";\r\n      position: absolute;\r\n      bottom: 0;\r\n      left: 0;\r\n      width: 26px;\r\n      background-color: var.$white;\r\n      height: 2px;\r\n      transition: all ease-in-out 0.3s;\r\n    }\r\n\r\n    &::before {\r\n      content: \"\";\r\n      position: absolute;\r\n      top: 0;\r\n      left: 0;\r\n      width: 26px;\r\n      background-color: var.$white;\r\n      height: 2px;\r\n      transition: all ease-in-out 0.3s;\r\n    }\r\n\r\n    span {\r\n      background-image: none;\r\n      height: 2px;\r\n      background-color: var.$baseclr;\r\n      width: 20px;\r\n      transition: all ease-in-out 0.3s;\r\n      display: flex;\r\n    }\r\n\r\n    &.active {\r\n      &::after {\r\n        transform: rotate(45deg) translate(-6px, -6px);\r\n      }\r\n\r\n      &::before {\r\n        transform: rotate(-45deg) translate(-8px, 8px);\r\n      }\r\n\r\n      span {\r\n        opacity: 0;\r\n      }\r\n    }\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAGA;;;;AADA;;;;;;;;;;;;;;AAYE;EAZF;;;;;;;;;;;;;;AAwBE;EAxBF;;;;;;AA8BI;EADF;;;;EAEI;;;;;;;;;EAUA;;;;;;;;;;EASE;;;;;AAKA;;;;;;AAQJ;;;;AAIA;;;;AAGE;;;;;;;;;;;;;;;AAeE;;;;;;;;;;;;AAgBE;;;;AAGF;;;;AAGE;;;;AAKF;;;;AAIE;;;;;;AAUN;;;;;AAQQ;;;;AAIA;;;;AASV;EACE;;;;;;;;;AAKE;;;;;;AAQJ;;;;;AAKA;;;;AAGF;EACE;;;;;;;;AAIE;;;;;;;;;;AAUA;;;;;;AAKE;EAEA;;;;;AAZJ;EAgBI;;;;;AAhBJ;;;;;;;;;;;AA+BI;;;;;;;;;;;AAWA;;;;;;;;;AAQA;;;;AAME;;;;AAIA", "debugId": null}}, {"offset": {"line": 1148, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/account/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/account/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/account/AccountLayout.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.Account_layout {\r\n  &_main {\r\n    display: flex;\r\n  }\r\n\r\n  // &_leftaside {\r\n\r\n  // }\r\n\r\n  &_rightaside {\r\n    width: calc(100% - 355px);\r\n    padding: 2.813rem 1.875rem;\r\n    color: var.$white;\r\n    position: relative;\r\n\r\n    @media (min-width: 992px) {\r\n      .mb-lg-4 {\r\n        margin-bottom: 1.875rem !important;\r\n      }\r\n    }\r\n\r\n    @media (max-width: 1199px) {\r\n      width: 100%;\r\n      padding: 2.813rem 1rem;\r\n    }\r\n  }\r\n}\r\n\r\n.sidebar_heading {\r\n  width: 100%;\r\n  margin-bottom: 26px;\r\n\r\n  @media screen and (max-width: 1199px) {\r\n    padding-left: 45px;\r\n  }\r\n\r\n  &_top {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    gap: 20px;\r\n  }\r\n\r\n  h2 {\r\n    font-size: 48px;\r\n    font-weight: 400;\r\n    font-family: \"Gilroy-Bold\", sans-serif;\r\n    display: inline-flex;\r\n\r\n    @media (max-width: 1199px) {\r\n      font-size: 1.688rem;\r\n    }\r\n\r\n    @media (max-width: 767px) {\r\n      font-size: 1.5rem;\r\n    }\r\n  }\r\n\r\n  &_icon {\r\n    button {\r\n      background-color: transparent;\r\n      border: 0;\r\n      color: var.$baseclr;\r\n      font-size: 1.15rem;\r\n      padding: 0;\r\n      font-weight: 600;\r\n      transition: all ease-in-out 0.3s;\r\n\r\n      @media (max-width: 991px) {\r\n        font-size: 14px;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: flex-end;\r\n      }\r\n\r\n      .me-2 {\r\n        margin-right: 10px !important;\r\n      }\r\n\r\n      .ms-2 {\r\n        margin-left: 10px !important;\r\n      }\r\n\r\n      svg {\r\n        margin-left: 10px;\r\n        transition: all ease-in-out 0.3s;\r\n        width: 16px;\r\n\r\n        @media (max-width: 991px) {\r\n          width: 14px;\r\n        }\r\n\r\n        @media (max-width: 767px) {\r\n          width: 10px;\r\n          margin-left: 0px;\r\n        }\r\n\r\n        path {\r\n          fill: var.$baseclr;\r\n        }\r\n      }\r\n\r\n      &:hover {\r\n        color: var.$yellow;\r\n\r\n        svg {\r\n          margin-left: 12px;\r\n\r\n          @media (max-width: 991px) {\r\n            margin-left: 10px;\r\n          }\r\n\r\n          path {\r\n            fill: var.$yellow;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.common_blackcard,\r\n.common_whitecard {\r\n  background-color: var.$clr191C23;\r\n  padding: 10px;\r\n  border-radius: 15px;\r\n  border: 1px solid var.$clr04498C;\r\n  width: 100%;\r\n\r\n  &_innerheader {\r\n    background-color: var.$clr031940;\r\n    padding: 0.625rem 1.25rem;\r\n    border-radius: 15px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-between;\r\n    font-family: \"Gilroy-Semibold\", sans-serif;\r\n    font-weight: 400;\r\n\r\n    @media (max-width: 991px) {\r\n      padding: 0.625rem 0.625rem;\r\n    }\r\n\r\n    p {\r\n      color: var.$white;\r\n\r\n      @media (max-width: 991px) {\r\n        font-size: 14px;\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        font-size: 14px;\r\n      }\r\n    }\r\n\r\n    &_content {\r\n      padding-right: 30px;\r\n      font-size: 18px;\r\n      font-weight: 400;\r\n\r\n      h6 {\r\n        font-weight: 600;\r\n      }\r\n\r\n      @media (max-width: 991px) {\r\n        padding-right: 15px;\r\n\r\n        h6 {\r\n          font-size: 18px !important;\r\n        }\r\n      }\r\n\r\n      @media (max-width: 767px) {\r\n        padding-right: 5px;\r\n      }\r\n\r\n      @media (max-width: 400px) {\r\n        h6 {\r\n          font-size: 14px !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_icon {\r\n      button {\r\n        background-color: transparent;\r\n        border: 0;\r\n        color: var.$baseclr;\r\n        font-size: 18px;\r\n        padding: 0;\r\n        font-weight: 600;\r\n        font-family: \"Gilroy-Semibold\", sans-serif;\r\n        transition: all ease-in-out 0.3s;\r\n        white-space: nowrap;\r\n\r\n        @media (max-width: 991px) {\r\n          font-size: 14px;\r\n          display: flex;\r\n          align-items: center;\r\n          justify-content: flex-end;\r\n        }\r\n\r\n        .me-2 {\r\n          margin-right: 10px !important;\r\n        }\r\n\r\n        .ms-2 {\r\n          margin-left: 10px !important;\r\n        }\r\n\r\n        .link-icon {\r\n          flex-shrink: 0;\r\n          height: auto;\r\n        }\r\n\r\n        svg {\r\n          transition: all ease-in-out 0.3s;\r\n          width: 16px;\r\n\r\n          @media (max-width: 991px) {\r\n            width: 14px;\r\n          }\r\n\r\n          @media (max-width: 767px) {\r\n            width: 10px;\r\n            margin-left: 0px;\r\n          }\r\n\r\n          path {\r\n            fill: var.$baseclr;\r\n          }\r\n        }\r\n\r\n        &:hover {\r\n          color: var.$yellow;\r\n\r\n          svg {\r\n            margin-left: 12px;\r\n\r\n            @media (max-width: 991px) {\r\n              margin-left: 10px;\r\n            }\r\n\r\n            path {\r\n              fill: var.$yellow;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &_tradeacct {\r\n      min-width: 200px;\r\n      width: 200px;\r\n\r\n      h6 {\r\n        background-color: var.$clr04498C;\r\n        padding: 8px 15px;\r\n        border-top-left-radius: 10px;\r\n        border-top-right-radius: 10px;\r\n        color: var.$white;\r\n        font-size: 1.25rem;\r\n        line-height: normal;\r\n\r\n        @media (max-width: 991px) {\r\n          font-size: 16px;\r\n        }\r\n      }\r\n\r\n      p {\r\n        background-color: var.$white;\r\n        padding: 8px 15px;\r\n        border-bottom-left-radius: 10px;\r\n        border-bottom-right-radius: 10px;\r\n        color: var.$black;\r\n        font-size: 1.25rem;\r\n        font-weight: 600;\r\n        margin: 0;\r\n        line-height: normal;\r\n\r\n        @media (max-width: 991px) {\r\n          font-size: 16px;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  &_innerbody {\r\n    padding: 1.25rem 0.625rem;\r\n  }\r\n}\r\n\r\n.connetionTable,\r\n.removePadding {\r\n\r\n  .common_blackcard_innerbody,\r\n  .common_whitecard_innerbody {\r\n    padding: 0;\r\n  }\r\n}\r\n\r\n.account_card {\r\n  &.pullcontent {\r\n    @media (max-width: 991px) {\r\n\r\n      .common_blackcard_innerheader_icon,\r\n      .common_whitecard_innerheader_icon {\r\n        margin-top: 10px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.common_whitecard {\r\n  background-color: white !important;\r\n\r\n  &_innerbody {\r\n    padding: 0.8rem 0.3rem 0px 0.3rem;\r\n  }\r\n}\r\n\r\n.account {\r\n  &_card {\r\n    .label {\r\n      color: var.$white;\r\n      font-size: 1.125rem;\r\n      font-weight: 600;\r\n    }\r\n\r\n    &_list {\r\n      &_btns {\r\n        display: flex;\r\n        justify-content: end;\r\n        align-items: center;\r\n        gap: 10px;\r\n\r\n        .btn-style {\r\n          min-height: 40px;\r\n          font-size: 16px !important;\r\n        }\r\n      }\r\n\r\n      &_form {\r\n        width: 40%;\r\n\r\n        .row {\r\n          width: 100% !important;\r\n        }\r\n      }\r\n\r\n      ul {\r\n        li {\r\n          display: flex;\r\n          justify-content: space-between;\r\n          margin-bottom: 1.25rem;\r\n          color: var.$white;\r\n          font-size: 1.125rem;\r\n          font-weight: 600;\r\n\r\n          @media (max-width: 991px) {\r\n            font-size: 15px;\r\n            margin-bottom: 1rem;\r\n          }\r\n\r\n          &:last-child {\r\n            margin-bottom: 0;\r\n          }\r\n\r\n          span {\r\n            color: #ffffff;\r\n          }\r\n        }\r\n\r\n      }\r\n\r\n      p {\r\n        color: var.$lightgreyclr;\r\n      }\r\n    }\r\n\r\n    &_redeem {\r\n      .form-control {\r\n        width: calc(100% - 136px);\r\n        margin-right: 10px;\r\n      }\r\n\r\n      ::placeholder {\r\n        color: #fff;\r\n        opacity: 1;\r\n      }\r\n\r\n      .btn-style {\r\n        font-size: 16px;\r\n        font-weight: 600;\r\n        line-height: 26px;\r\n        min-width: 150px;\r\n        padding: 8px 15px;\r\n        min-height: 56px;\r\n\r\n        @media (max-width: 991px) {\r\n          font-size: 14px;\r\n          line-height: 24px;\r\n          min-width: 130px;\r\n          padding: 8px 15px;\r\n          min-height: 52px;\r\n        }\r\n      }\r\n\r\n      .error-messages {\r\n        .success {\r\n          color: #32CD33;\r\n        }\r\n\r\n        .invalid {\r\n          color: #ff696a;\r\n        }\r\n      }\r\n    }\r\n\r\n    &_checkup {\r\n      &_verify {\r\n        width: calc(100% - 150px);\r\n        padding-right: 30px;\r\n\r\n        @media (max-width: 991px) {\r\n          width: calc(100% - 100px);\r\n          padding-right: 0;\r\n        }\r\n      }\r\n\r\n      &_chart {\r\n        width: 150px;\r\n        text-align: center;\r\n\r\n        @media (max-width: 991px) {\r\n          width: 100px;\r\n\r\n          .CircularProgressbar_text h6 {\r\n            font-size: 14px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &_subscription {\r\n      &_list {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n\r\n        li {\r\n          width: 25%;\r\n\r\n          @media (max-width: 767px) {\r\n            width: 50%;\r\n            padding: 5px 0;\r\n\r\n            &:nth-child(2n) {\r\n              text-align: right;\r\n            }\r\n          }\r\n\r\n          p {\r\n            color: #ffffff;\r\n            font-weight: 600;\r\n            margin-top: 0.5rem;\r\n\r\n            @media (max-width: 991px) {\r\n              font-size: 14px;\r\n              line-height: 20px;\r\n            }\r\n\r\n            @media (max-width: 767px) {\r\n              margin-top: 0;\r\n            }\r\n          }\r\n\r\n          h6 {\r\n            @media (max-width: 991px) {\r\n              font-size: 15px;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    &_table {\r\n      .simple_table_imgIcon {\r\n        min-width: 20px;\r\n      }\r\n\r\n      .tableless {\r\n        border: 0;\r\n\r\n        &::before {\r\n          display: none;\r\n        }\r\n\r\n        .common_table {\r\n          tr {\r\n\r\n            th,\r\n            td {\r\n              border: 0;\r\n            }\r\n\r\n            td {\r\n              border-bottom: 1px solid var.$borderclr;\r\n              color: var.$white;\r\n              font-weight: 600 !important;\r\n            }\r\n          }\r\n\r\n          thead {\r\n            tr {\r\n              border-radius: 15px;\r\n\r\n              th {\r\n                background-color: var.$clr031940;\r\n                padding: 10px 15px;\r\n                font-size: 1rem;\r\n\r\n                &:first-child {\r\n                  border-top-left-radius: 15px;\r\n                  border-bottom-left-radius: 15px;\r\n                }\r\n\r\n                &:last-child {\r\n                  border-top-right-radius: 15px;\r\n                  border-bottom-right-radius: 15px;\r\n                }\r\n              }\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    .add_phone_number,\r\n    .add_number,\r\n    .blue_text_btn {\r\n      background-color: transparent;\r\n      border: 0;\r\n      color: var.$baseclr;\r\n      font-size: 1.25rem;\r\n      font-weight: 600;\r\n      line-height: 24.5px;\r\n      letter-spacing: -0.10000000149011612px;\r\n      transition: all ease-in-out 0.3s;\r\n      padding: 0;\r\n\r\n      @media (max-width: 991px) {\r\n        font-size: 1rem;\r\n      }\r\n\r\n      &:hover {\r\n        color: var.$yellow;\r\n      }\r\n    }\r\n\r\n    .add_number,\r\n    .blue_text_btn {\r\n\r\n      svg,\r\n      img {\r\n        width: 16px;\r\n        height: 16px;\r\n        margin-right: 10px;\r\n\r\n        path {\r\n          fill: var.$baseclr;\r\n        }\r\n      }\r\n\r\n      &:hover {\r\n        svg {\r\n          path {\r\n            fill: var.$yellow;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.CircularProgressbar_text h6 {\r\n  font-size: 14px !important;\r\n}\r\n\r\n.account-custom-select {\r\n  position: relative;\r\n\r\n  .header {\r\n    min-height: 56px;\r\n    box-shadow: none;\r\n    outline: none;\r\n    width: 100%;\r\n    padding: 0.5rem 1.25rem;\r\n    border-radius: 1rem;\r\n    background-color: #ffffff4d;\r\n    color: #fff;\r\n    font-size: 1rem;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    cursor: pointer;\r\n\r\n    span {\r\n      width: 100% !important;\r\n    }\r\n  }\r\n\r\n  .body {\r\n    width: 100%;\r\n    padding: 0.5rem 1.25rem;\r\n    position: absolute;\r\n    top: 65px;\r\n    border-radius: 1rem;\r\n    border: 1px solid #ffffff4d;\r\n    background-color: #191c23;\r\n    max-height: 250px;\r\n    overflow-y: scroll;\r\n    z-index: 999;\r\n\r\n\r\n    .search {\r\n      background-color: #ffffff4d;\r\n      margin: 0.5rem 0;\r\n      padding: 0.5rem 1.25rem;\r\n      width: 100%;\r\n      border-radius: 1rem;\r\n      display: flex;\r\n      gap: 10px;\r\n      align-items: center;\r\n\r\n      input {\r\n        background-color: transparent;\r\n        width: 100%;\r\n        color: #fff;\r\n\r\n        &:focus-visible {\r\n          outline: none !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    ul {\r\n      li {\r\n        cursor: pointer;\r\n        padding: 10px;\r\n        border-radius: 8px;\r\n        border-bottom: 0 !important;\r\n        margin-bottom: 0 !important;\r\n\r\n        &:hover {\r\n          background-color: #ffffff4d;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.account-custom-select.simple {\r\n  .header {\r\n    border: 1px solid #********;\r\n    background-color: #f2f2f2;\r\n    min-height: 34px;\r\n    padding: 7px 10px;\r\n    border-radius: 10px;\r\n\r\n    span {\r\n      color: black !important;\r\n      font-size: 14px;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  .body {\r\n    width: 396px;\r\n    padding: 10px 12px;\r\n    top: 45px;\r\n    right: 0;\r\n    border-radius: 0.94rem;\r\n    border: 1px solid #0606061a;\r\n    background-color: white;\r\n    max-height: 200px;\r\n\r\n    @media only screen and (max-width: 500px) {\r\n      width: auto;\r\n    }\r\n\r\n    &.align-left {\r\n      left: 0;\r\n      right: auto;\r\n    }\r\n\r\n    &.align-right {\r\n      right: 0;\r\n      left: auto;\r\n    }\r\n\r\n    &::-webkit-scrollbar {\r\n      display: block;\r\n    }\r\n\r\n    .option-heading {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 5px;\r\n      margin: 6px 0px 2px 0px;\r\n\r\n      p {\r\n        color: black;\r\n        font-size: 14px;\r\n        font-weight: 700;\r\n      }\r\n    }\r\n\r\n    ul {\r\n      li {\r\n        font-size: 14px;\r\n        font-weight: 600;\r\n        padding: 4px 0px;\r\n        border-radius: 0 !important;\r\n        border-bottom: 1px solid #********;\r\n\r\n        &:hover {\r\n          background-color: rgba(211, 211, 211, 0.166);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.width-autofit {\r\n  width: fit-content !important;\r\n}\r\n\r\n.text_00ADEF {\r\n  color: #00ADEF !important;\r\n}\r\n\r\ninput::-webkit-outer-spin-button,\r\ninput::-webkit-inner-spin-button {\r\n  -webkit-appearance: none;\r\n  margin: 0;\r\n}\r\n\r\n.switch {\r\n  position: relative;\r\n  display: inline-block;\r\n  width: 50px;\r\n  height: 28px;\r\n}\r\n\r\n.switch input {\r\n  opacity: 0;\r\n  width: 0;\r\n  height: 0;\r\n}\r\n\r\n.slider {\r\n  position: absolute;\r\n  cursor: pointer;\r\n  top: 0;\r\n  left: 0;\r\n  right: 0;\r\n  bottom: 0;\r\n  background-color: #fff;\r\n  transition: 0.4s;\r\n  border-radius: 34px;\r\n}\r\n\r\n.slider:before {\r\n  position: absolute;\r\n  content: \"\";\r\n  height: 22px;\r\n  width: 22px;\r\n  left: 3px;\r\n  bottom: 3px;\r\n  background-color: #9c9a9f;\r\n  transition: 0.4s;\r\n  border-radius: 50%;\r\n}\r\n\r\ninput:checked+.slider {\r\n  background-color: #0099d1;\r\n}\r\n\r\ninput:checked+.slider:before {\r\n  transform: translateX(22px);\r\n  background-color: white;\r\n}\r\n\r\n.modal_overlay {\r\n  position: fixed;\r\n  top: 0;\r\n  left: 0;\r\n  width: 100vw;\r\n  height: 100vh;\r\n  background-color: rgba(0, 0, 0, 0.6);\r\n  z-index: 9998;\r\n\r\n  .search_section,\r\n  .modal-body {\r\n    position: fixed;\r\n    top: 50%;\r\n    left: 50%;\r\n    transform: translate(-50%, -50%);\r\n    z-index: 1000;\r\n    width: 1000px;\r\n    background-color: #031940;\r\n    padding: 20px;\r\n    border-radius: 15px;\r\n    max-height: 90vh;\r\n    border: 1px solid #00adef;\r\n\r\n    @media (max-width: 1023px) {\r\n      width: 90%;\r\n    }\r\n\r\n    h4 {\r\n      font-size: 28px;\r\n      font-weight: 400;\r\n      margin-bottom: 20px;\r\n    }\r\n\r\n    .search_header {\r\n      margin-bottom: 20px;\r\n\r\n      .closing_Section {\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 1rem;\r\n        flex-shrink: 0;\r\n        height: 100%;\r\n\r\n        @media (max-width: 1023px) {\r\n          padding-right: 8px;\r\n        }\r\n\r\n        img {\r\n          cursor: pointer;\r\n        }\r\n      }\r\n\r\n      .search {\r\n        height: 100%;\r\n      }\r\n\r\n      p {\r\n        font-size: 18px;\r\n        font-weight: 600;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      span {\r\n        white-space: nowrap;\r\n        color: #ffffff99;\r\n      }\r\n\r\n      .btn-style {\r\n        min-width: 100%;\r\n      }\r\n\r\n      button {\r\n        span {\r\n          color: #fff !important;\r\n        }\r\n      }\r\n    }\r\n\r\n    .search {\r\n      width: 100%;\r\n      background-color: #ffffff33;\r\n      padding: 10px 20px;\r\n      border-radius: 15px;\r\n      display: flex;\r\n      gap: 10px;\r\n\r\n      input {\r\n        background-color: transparent;\r\n        width: 100%;\r\n        height: 100%;\r\n        color: #ffffff99;\r\n\r\n        &:focus {\r\n          box-shadow: none;\r\n          outline: 0;\r\n        }\r\n      }\r\n    }\r\n\r\n    @keyframes fadeOut {\r\n      0% {\r\n        opacity: 0.9;\r\n      }\r\n\r\n      50% {\r\n        opacity: 1;\r\n      }\r\n\r\n      90% {\r\n        opacity: 0.9;\r\n      }\r\n\r\n      100% {\r\n        opacity: 0;\r\n      }\r\n    }\r\n\r\n    .modal-footer-btn {\r\n      min-height: 40px !important;\r\n      font-size: 18px;\r\n    }\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AAAE;;;;AAQA;;;;;;;AAME;EACE;;;;;AAKF;EAZF;;;;;;AAmBF;;;;;AAIE;EAJF;;;;;AAQE;;;;;;;AAOA;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;AAgBE;;;;;;;;;;AASE;EATF;;;;;;;;AAgBE;;;;AAIA;;;;AAIA;;;;;;AAKE;EALF;;;;;AASE;EATF;;;;;;AAcE;;;;AAKF;;;;AAGE;;;;AAGE;EAHF;;;;;AAOE;;;;AASV;;;;;;;;AAQE;;;;;;;;;;;AAUE;EAVF;;;;;AAcE;;;;AAGE;EAHF;;;;;AAOE;EAPF;;;;;AAYA;;;;;;AAKE;;;;AAIA;EATF;;;;EAYI;;;;;AAKF;EAjBF;;;;;AAqBE;EACE;;;;;AAOF;;;;;;;;;;;;AAWE;EAXF;;;;;;;;AAkBE;;;;AAIA;;;;AAIA;;;;;AAKA;;;;;AAIE;EAJF;;;;;AAQE;EARF;;;;;;AAaE;;;;AAKF;;;;AAGE;;;;AAGE;EAHF;;;;;AAOE;;;;AAQR;;;;;AAIE;;;;;;;;;;AASE;EATF;;;;;AAcA;;;;;;;;;;;;AAWE;EAXF;;;;;AAkBJ;;;;AAQA;;;;AAQE;EAEE;;;;;AAQN;;;;AAGE;;;;AAOE;;;;;;AAOE;;;;;;;AAME;;;;;AAMF;;;;AAGE;;;;AAMA;;;;;;;;;AAQE;EARF;;;;;;AAaE;;;;AAIA;;;;AAOJ;;;;AAMA;;;;;AAKA;;;;;AAKA;;;;;;;;;AAQE;EARF;;;;;;;;;AAkBE;;;;AAIA;;;;AAOF;;;;;AAIE;EAJF;;;;;;AAUA;;;;;AAIE;EAJF;;;;EAOI;;;;;AAQJ;;;;;AAIE;;;;AAGE;EAHF;;;;;EAOI;;;;;AAKF;;;;;;AAKE;EALF;;;;;;AAUE;EAVF;;;;;AAgBE;EADF;;;;;AAUJ;;;;AAIA;;;;AAGE;;;;AAOI;;;;AAKA;;;;;;AAQA;;;;AAGE;;;;;;AAKE;;;;;AAKA;;;;;AAWZ;;;;;;;;;;;;AAaE;EAbF;;;;;AAiBE;;;;AAQA;;;;;;AAME;;;;AAOE;;;;AASV;;;;AAIA;;;;AAGE;;;;;;;;;;;;;;;;AAeE;;;;AAKF;;;;;;;;;;;;;AAaE;;;;;;;;;;;AAUE;;;;;;AAKE;;;;AAOF;;;;;;;;AAOE;;;;AASN;;;;;;;;AAOE;;;;;;AAOF;;;;;;;;;;;AAUE;EAVF;;;;;AAcE;;;;;AAKA;;;;;AAKA;;;;AAIA;;;;;;;AAME;;;;;;AAQA;;;;;;;;AAOE;;;;AAQR;;;;;AAIA;;;;AAIA;;;;;AAAA;;;;;AAMA;;;;;;;AAOA;;;;;;AAMA;;;;;;;;;;;;AAYA;;;;;;;;;;;;AAYA;;;;AAIA;;;;;AAKA;;;;;;;;;;AASE;;;;;;;;;;;;;;AAcE;EAdF;;;;;AAkBE;;;;;;AAMA;;;;AAGE;;;;;;;;AAOE;EAPF;;;;;AAWE;;;;AAKF;;;;AAIA;;;;;;AAMA;;;;;AAKA;;;;AAKE;;;;AAMJ;;;;;;;;;AAQE;;;;;;;AAME;;;;;AAOJ;;;;;;;;;;;;;;;;;;AAkBA", "debugId": null}}, {"offset": {"line": 2065, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/CommonSearch.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.commonSearch {\r\n  display: flex;\r\n  position: relative;\r\n  align-items: center;\r\n\r\n  &.searchBtn {\r\n    .form-control {\r\n      padding: 0.5rem 1rem;\r\n      background-color: var.$white;\r\n      color: var.$black;\r\n      border: 0;\r\n      border-top-left-radius: 10rem;\r\n      border-bottom-left-radius: 10rem;\r\n      min-height: 50px;\r\n      width: calc(100% - 54px);\r\n      min-width: auto;\r\n      font-weight: 600;\r\n\r\n      &::placeholder {\r\n        color: var.$textclr;\r\n        opacity: 1;\r\n      }\r\n\r\n      &:focus {\r\n        box-shadow: none;\r\n        outline: 0;\r\n        background-color: var.$white;\r\n        color: var.$black;\r\n      }\r\n    }\r\n  }\r\n\r\n  .form-control {\r\n    padding: 0.5rem 1rem;\r\n    padding-left: 50px;\r\n    background-color: rgba(255, 255, 255, 0.3);\r\n    color: var.$white;\r\n    border: 0;\r\n    border-radius: 15px;\r\n    min-height: 70px;\r\n    width: auto;\r\n    width: 400px;\r\n    font-size: 1.25rem;\r\n    appearance: none;\r\n    -webkit-appearance: none;\r\n\r\n    @media (max-width: 991px) {\r\n      font-size: 16px;\r\n      min-height: 56px;\r\n      padding-left: 40px;\r\n    }\r\n\r\n    &:hover {\r\n      appearance: none;\r\n    }\r\n\r\n    &::placeholder {\r\n      color: rgba(255, 255, 255, 0.8);\r\n      opacity: 1;\r\n    }\r\n\r\n    &:disabled {\r\n      background-color: transparent;\r\n    }\r\n\r\n    &:focus {\r\n      box-shadow: none;\r\n      outline: 0;\r\n      background-color: rgba(255, 255, 255, 0.3);\r\n      color: var.$white;\r\n      border: 0;\r\n    }\r\n  }\r\n\r\n  .onlyIcon {\r\n    position: absolute;\r\n    left: 20px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    cursor: pointer;\r\n\r\n    @media (max-width: 991px) {\r\n      left: 15px;\r\n    }\r\n  }\r\n\r\n  .btnIcon {\r\n    cursor: pointer;\r\n    width: 54px;\r\n    min-height: 50px;\r\n    background-color: var.$baseclr;\r\n    border-top-right-radius: 10rem;\r\n    border-bottom-right-radius: 10rem;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: center;\r\n    transition: all ease-in-out 0.3s;\r\n\r\n    &:hover {\r\n      background-color: var.$yellow;\r\n    }\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;;;AAMI;;;;;;;;;;;;;AAYE;;;;;AAKA;;;;;;;AASJ;;;;;;;;;;;;;;AAcE;EAdF;;;;;;;AAoBE;;;;;;AAIA;;;;;AAKA;;;;AAIA;;;;;;;;AASF;;;;;;;;AAOE;EAPF;;;;;AAYA;;;;;;;;;;;;;AAYE", "debugId": null}}, {"offset": {"line": 2177, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/account/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/account/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/account/Security.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.security_sec {\r\n  .account_card_list {\r\n    ul {\r\n      li {\r\n        justify-content: flex-start;\r\n\r\n        span {\r\n          padding-right: 15px;\r\n\r\n          @media screen and (min-width: 768px) {\r\n            // width: 200px;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    p {\r\n      color: var.$textclr;\r\n    }\r\n  }\r\n\r\n  .table {\r\n    &_heading {\r\n      border: 1px solid var.$borderclr;\r\n      border-radius: 15px;\r\n      padding: 10px 1.25rem;\r\n    }\r\n  }\r\n\r\n}\r\n\r\n.authCorrectIcon {\r\n  position: relative;\r\n\r\n  .checkIcon {\r\n    position: absolute;\r\n    right: -40px;\r\n    top: 10px;\r\n\r\n    @media (max-width: 767px) {\r\n      right: -30px;\r\n    }\r\n  }\r\n\r\n  svg {\r\n    height: 22;\r\n  }\r\n}\r\n\r\n.modal-content {\r\n  background-color: #031940 !important;\r\n  border: 1px solid var.$clr04498C;\r\n  border-radius: 15px;\r\n  color: #ffffff;\r\n}\r\n\r\n.account_card_list_btns {\r\n  display: flex;\r\n  justify-content: end;\r\n  align-items: center;\r\n  gap: 10px;\r\n}\r\n\r\n\r\n/* Responsive styles for small screens */\r\n@media (max-width: 360px) {\r\n  .account_card_list_btns {\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    gap: 5px;\r\n  }\r\n}\r\n\r\n@media (max-width: 360px) {\r\n  .confirm-modal-btn {\r\n    justify-content: center !important;\r\n    gap: 8px !important;\r\n    flex-wrap: wrap;\r\n  }\r\n}\r\n\r\n/* Restore code input placeholder styling */\r\n.restore-code-input::placeholder {\r\n  color: #fff !important;\r\n  opacity: 1;\r\n}\r\n\r\n/* Mobile responsiveness for UseRestoreCodeModal at 320px viewport */\r\n@media (max-width: 320px) {\r\n  .modal-dialog {\r\n    margin: 10px;\r\n    max-width: calc(100vw - 20px);\r\n  }\r\n\r\n  .custom-modal-content {\r\n    margin: 0;\r\n    max-width: 100%;\r\n  }\r\n\r\n  .modal-xl {\r\n    max-width: calc(100vw - 20px) !important;\r\n  }\r\n\r\n  /* Ensure modal content fits within viewport */\r\n  .modal-content {\r\n    max-width: 100%;\r\n    margin: 0;\r\n  }\r\n\r\n  /* Adjust padding for very small screens */\r\n  .modal-body {\r\n    padding: 1rem 0.75rem;\r\n  }\r\n\r\n  /* Adjust modal inner content padding for mobile */\r\n  .custom-modal-content > div {\r\n    padding-left: 1rem !important;\r\n    padding-right: 1rem !important;\r\n    padding-top: 1.5rem !important;\r\n    padding-bottom: 1.5rem !important;\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AAEM;;;;AAGE;;;;AAUJ;;;;AAMA;;;;;;AASJ;;;;AAGE;;;;;;AAKE;EALF;;;;;AAUA;;;;AAKF;;;;;;;AAOA;;;;;;;AASA;EACE;;;;;;;EAIE;;;;;;;AAOA;;;;;AAOF;EACA;;;;;EAOE;;;;;EAGF;;;;EAME;;;;;EAIF;;;;EAMA", "debugId": null}}, {"offset": {"line": 2283, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/account/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/account/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/account/AccountDetails.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.account_details {\r\n  .account_card_list {\r\n    .form-control {\r\n      color: #fff !important;\r\n    }\r\n\r\n    // .form-select {\r\n    //   --bs-form-select-bg-img: url(data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e);\r\n    // }\r\n\r\n    ul {\r\n      li {\r\n        justify-content: flex-start !important;\r\n\r\n\r\n        @media screen and (max-width: 767px) {\r\n          justify-content: space-between;\r\n        }\r\n\r\n        span {\r\n          padding-right: 15px;\r\n\r\n          // @media screen and (min-width: 768px) {\r\n          //   width: 250px;\r\n          // }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.add_plusicon {\r\n  button {\r\n    svg {\r\n      width: 16px;\r\n      height: 16px;\r\n      margin-right: 10px;\r\n\r\n      path {\r\n        fill: var.$baseclr;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.account_setup_phone_number {\r\n  label {\r\n    cursor: pointer;\r\n\r\n    p {\r\n      font-weight: 300;\r\n    }\r\n  }\r\n}\r\n\r\n.new-address-section {\r\n  ul {\r\n    li {\r\n      border-bottom: 1px solid #666;\r\n      padding-bottom: 1.25rem;\r\n      align-items: center;\r\n\r\n      &:last-child {\r\n        padding-bottom: 0 !important;\r\n        border-bottom: none !important;\r\n      }\r\n    }\r\n  }\r\n\r\n  .show-address-details {\r\n    display: flex;\r\n    flex-direction: column;\r\n    gap: 4px;\r\n\r\n    .name {\r\n      font-size: 1.125rem !important;\r\n      font-weight: 600 !important;\r\n      color: #fff;\r\n      margin-top: 0 !important;\r\n    }\r\n\r\n    .address,\r\n    .city {\r\n      font-size: 1rem;\r\n      font-weight: 400;\r\n      color: #fff;\r\n      margin-top: 0 !important;\r\n    }\r\n\r\n    .remove-address-confirmation {\r\n      p {\r\n        font-size: 1.125rem !important;\r\n        font-weight: 600 !important;\r\n        color: #fff;\r\n      }\r\n\r\n      .btns {\r\n        margin-top: 10px;\r\n        display: flex;\r\n        align-items: center;\r\n        gap: 10px;\r\n\r\n        .btn-style {\r\n          min-height: 40px;\r\n        }\r\n      }\r\n\r\n    }\r\n  }\r\n\r\n  .btns {\r\n    display: flex;\r\n    align-items: center;\r\n    gap: 10px;\r\n\r\n    button {\r\n      display: flex;\r\n      align-items: center;\r\n      gap: 10px;\r\n\r\n      svg {\r\n        width: 16px;\r\n        height: 16px;\r\n\r\n        path {\r\n          fill: var.$baseclr;\r\n        }\r\n      }\r\n\r\n      span {\r\n        color: #00adef;\r\n        font-size: 18px;\r\n        padding: 0;\r\n        font-weight: 600;\r\n        font-family: Gilroy-Semibold, sans-serif;\r\n        transition: all .3s ease-in-out;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      &:hover {\r\n        span {\r\n          color: var.$yellow;\r\n        }\r\n\r\n        svg {\r\n          path {\r\n            fill: var.$yellow;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.border-bottom-none {\r\n  border-bottom: 0 !important;\r\n}\r\n\r\n// Account header layout with status indicator\r\n.account_header_main {\r\n  display: flex;\r\n  flex-direction: column;\r\n  gap: 8px;\r\n\r\n  h6 {\r\n    margin: 0;\r\n    font-size: 1.125rem;\r\n    font-weight: 600;\r\n    color: #fff;\r\n    line-height: 1.2;\r\n  }\r\n\r\n  @media (min-width: 768px) {\r\n    flex-direction: row;\r\n    align-items: center;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n.account_status_indicator {\r\n  display: flex;\r\n  align-items: center;\r\n\r\n  @media (max-width: 767px) {\r\n    margin-left: 0;\r\n  }\r\n}\r\n\r\n// Status indicator styles for account details components\r\n.status_indicator {\r\n  display: flex;\r\n  align-items: center;\r\n  gap: 6px;\r\n  padding: 4px 8px;\r\n  border-radius: 6px;\r\n  background-color: rgba(255, 255, 255, 0.05);\r\n  border: 1px solid rgba(255, 255, 255, 0.1);\r\n  transition: all 0.2s ease-in-out;\r\n\r\n  span {\r\n    font-weight: 500;\r\n    font-size: 14px;\r\n    color: rgba(255, 255, 255, 0.9);\r\n    white-space: nowrap;\r\n    line-height: 1;\r\n  }\r\n\r\n  svg,\r\n  img {\r\n    width: 16px;\r\n    height: 16px;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  // State-specific styling\r\n  &.status-loading {\r\n    background-color: rgba(0, 173, 239, 0.1);\r\n    border-color: rgba(0, 173, 239, 0.2);\r\n\r\n    span {\r\n      color: #00adef;\r\n    }\r\n  }\r\n\r\n  &.status-success {\r\n    background-color: rgba(40, 167, 69, 0.1);\r\n    border-color: rgba(40, 167, 69, 0.2);\r\n\r\n    span {\r\n      color: #28a745;\r\n    }\r\n  }\r\n\r\n  &.status-error {\r\n    background-color: rgba(220, 53, 69, 0.1);\r\n    border-color: rgba(220, 53, 69, 0.2);\r\n\r\n    span {\r\n      color: #dc3545;\r\n    }\r\n  }\r\n\r\n  &.status-default {\r\n    background-color: rgba(108, 117, 125, 0.1);\r\n    border-color: rgba(108, 117, 125, 0.2);\r\n\r\n    span {\r\n      color: #fff;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 767px) {\r\n    padding: 3px 6px;\r\n\r\n    svg,\r\n    img {\r\n      width: 14px;\r\n      height: 14px;\r\n    }\r\n  }\r\n}\r\n\r\n.account_create_username {\r\n  .username-rules {\r\n    ul {\r\n      li {\r\n        margin-bottom: 0.5rem !important;\r\n        color: #fff;\r\n        font-size: 1rem;\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AACE;;;;AASI;;;;AAIE;EAJF;;;;;AAQE;;;;AAcJ;;;;;;AAKE;;;;AAQJ;;;;AAGE;;;;AAQA;;;;;;AAKE;;;;;AAOJ;;;;;;AAKE;;;;;;;AAOA;;;;;;;AASE;;;;;;AAMA;;;;;;;AAME;;;;AAQN;;;;;;AAUI;;;;;AAIE;;;;AAKF;;;;;;;;;;AAWE;;;;AAKE;;;;AASV;;;;AAKA;;;;;;AAKE;;;;;;;;AAQA;EAbF;;;;;;;AAoBA;;;;;AAIE;EAJF;;;;;AAUA;;;;;;;;;;;AAUE;;;;;;;;AAQA;;;;;;AAQA;;;;;AAIE;;;;AAKF;;;;;AAIE;;;;AAKF;;;;;AAIE;;;;AAKF;;;;;AAIE;;;;AAKF;EA9DF;;;;EAiEI;;;;;;AAWE", "debugId": null}}, {"offset": {"line": 2521, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/CommonTooltip.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.tooltip-container {\r\n  position: relative;\r\n  display: inline-block;\r\n  cursor: pointer;\r\n\r\n  img {\r\n    max-width: 20px;\r\n    min-width: 20px;\r\n    min-height: 20px;\r\n    max-height: 20px;\r\n  }\r\n}\r\n\r\n.tooltip-wrapper {\r\n  flex-shrink: 0;\r\n  cursor: pointer;\r\n}\r\n\r\n.tooltip-box {\r\n  position: absolute;\r\n  padding: 5px 10px;\r\n  border-radius: 5px;\r\n  z-index: 1000;\r\n  min-width: 300px;\r\n  width: 300px;\r\n  background-color: hwb(210 1% 65% / 0.699);\r\n  color: var.$white;\r\n  text-align: left;\r\n  padding: 10px 15px;\r\n  border-radius: 5px;\r\n  backdrop-filter: blur(6px);\r\n  pointer-events: auto !important;\r\n\r\n  p,\r\n  a {\r\n    font-size: 0.875rem !important;\r\n    font-weight: 300;\r\n    line-height: 20px;\r\n\r\n    @media screen and (max-width: 991px) {\r\n      font-size: 14px;\r\n      line-height: 18px;\r\n    }\r\n  }\r\n\r\n  @media screen and (max-width: 991px) {\r\n    min-width: 200px;\r\n    width: 200px;\r\n  }\r\n\r\n}\r\n\r\n.tooltip-top-left {\r\n  top: 0;\r\n  right: 0;\r\n  transform: translateY(-100%);\r\n}\r\n\r\n.tooltip-top-right {\r\n  top: 0;\r\n  left: 0;\r\n  transform: translateY(-100%);\r\n}\r\n\r\n.tooltip-bottom-left {\r\n  bottom: 0;\r\n  right: 0;\r\n  transform: translateY(100%);\r\n}\r\n\r\n.tooltip-bottom-right {\r\n  bottom: 0;\r\n  left: 0;\r\n  transform: translateY(100%);\r\n}\r\n\r\n.tooltip-center-bottom {\r\n  top: 25px;\r\n  left: 50%;\r\n  transform: translateX(-50%);\r\n}\r\n\r\n.tooltip-center-top {\r\n  bottom: 100%;\r\n  left: 50%;\r\n  transform: translateX(-50%) translateY(-10px);\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;;;AAKE;;;;;;;AAQF;;;;;AAKA;;;;;;;;;;;;;;;AAaE;;;;;;AAKE;EAGA;;;;;EACE;;;;;;AAKJ;;;;;;AAOF;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA;;;;;;AAMA", "debugId": null}}, {"offset": {"line": 2613, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/theme/_var.scss", "turbopack:///turbopack:///[project]/src/css/common/C:/Users/<USER>/Desktop/Jerrax/Frontend/src/css/common/CommonTable.scss"], "sourcesContent": ["$basefont: '<PERSON><PERSON>', sans-serif;\r\n$basefont: '<PERSON><PERSON>', 'sans-serif';\r\n\r\n:root {\r\n  --font-gilroy: '<PERSON><PERSON>', sans-serif;\r\n}\r\n\r\n// color code\r\n$themeclr: #011132;\r\n$baseclr: #00adef;\r\n$baseclrhover: #0099d1;\r\n$white: #ffffff;\r\n$black: #000000;\r\n$textclr: #c5c5d5;\r\n$greytext: #9c9a9f;\r\n$darkgreytext: #808080;\r\n$green: #32cd33;\r\n$grayBtn: #5E6165;\r\n$grayBtnHover: #708090;\r\n$greenbtnhover: #2bb72b;\r\n$greenlighttext: #7aff67;\r\n$greenlightbg: #7af870;\r\n$red: #ff0302;\r\n$redlighttext: #d54d3f;\r\n$redlightclr: #ff696a;\r\n$redbghover: #e65f60;\r\n$yellow: #fea500;\r\n$yellowBtnHover: #c9870d;\r\n$yellowlighttext: #feff14;\r\n$borderclr: #666666;\r\n$cardbg: rgba(159, 159, 159, 0.1);\r\n$inputbgclr: rgba(255, 255, 255, 0.3);\r\n$blackbg: #160125;\r\n$bghover: #084872;\r\n$clr000607: #000607;\r\n$clr031940: #031940;\r\n$clr064197: #064197;\r\n$clr042053: #042053;\r\n$clr001331: #001331;\r\n$clr032251: #032251;\r\n$clr04498C: #04498c;\r\n$clr191C23: #191c23;\r\n$lightwhiteclr: rgba(255, 255, 255, 0.5);\r\n$bluelightclr: rgba(0, 173, 239, 0.15);\r\n$lightgreyclr: #c5c5c5;\r\n$clr1E222D: #1e222d;\r\n$clr2A2E39: #2a2e39;\r\n$clr283f67: #283f67;\r\n$clrd9d9d9: #d9d9d9;\r\n$clrc5c5d5: #c5c5d5;\r\n$clre9e9e9: #e9e9e9;\r\n$clre6e6e6: #e6e6e6;\r\n$clr00b9ff: #00b9ff;\r\n\r\n// $gradient: linear-gradient(121.07deg, #B482ED -3.32%, #000000 95.56%);\r\n$radialgradientblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.2) 21.5%,\r\n    rgba(0, 83, 153, 0.2) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.05) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientGreen: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(50, 205, 51, 0.4) 0%,\r\n    rgba(50, 205, 51, 0.15) 45.5%,\r\n    rgba(50, 205, 51, 0.4) 98%);\r\n$gradientRed: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(255, 105, 106, 0.4) 0%,\r\n    rgba(255, 105, 106, 0.2) 50%,\r\n    rgba(255, 105, 106, 0.4) 100%);\r\n$gradientYellow: linear-gradient(0deg,\r\n    rgba(255, 255, 255, 0.1),\r\n    rgba(255, 255, 255, 0.1)),\r\n  linear-gradient(270.33deg,\r\n    rgba(254, 165, 0, 0.4) 0%,\r\n    rgba(254, 165, 0, 0.2) 50%,\r\n    rgba(254, 165, 0, 0.4) 100%);\r\n$gradientcardblue: radial-gradient(50% 50% at 50% 50%,\r\n    rgba(0, 185, 255, 0.5) 21.5%,\r\n    rgba(0, 83, 153, 0.5) 100%),\r\n  linear-gradient(135deg,\r\n    rgba(255, 255, 255, 0) 0%,\r\n    rgba(255, 255, 255, 0.2) 47.5%,\r\n    rgba(255, 255, 255, 0) 100%);\r\n$gradientblackbg: linear-gradient(to right, #2a2e39, #1e222d);\r\n$gradientbluebg: linear-gradient(to right, #283f67, #031940);", "@use \"../theme/var\";\r\n\r\n.tableless {\r\n  border: 1px solid var.$baseclr;\r\n  border-radius: 2rem;\r\n  position: relative;\r\n  z-index: 1;\r\n\r\n  &::before {\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 0;\r\n    left: 0;\r\n    width: 100%;\r\n    height: 100%;\r\n    background-image: url(\"https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png\");\r\n    background-position: center;\r\n    background-repeat: no-repeat;\r\n    background-size: 100%;\r\n    border-radius: 2rem;\r\n    z-index: -1;\r\n  }\r\n\r\n  .common_table {\r\n    tr {\r\n\r\n      th,\r\n      td {\r\n        padding: 1.6rem 1rem;\r\n        white-space: nowrap;\r\n        vertical-align: middle;\r\n        border: 1px solid #34415b;\r\n\r\n        @media (max-width: 767px) {\r\n          padding: 1.5rem 0.5rem;\r\n        }\r\n\r\n        .clickIcon {\r\n          cursor: pointer;\r\n        }\r\n      }\r\n\r\n      td {\r\n        font-weight: 500 !important;\r\n      }\r\n    }\r\n\r\n    thead {\r\n      border: none;\r\n\r\n      tr {\r\n        position: sticky;\r\n        top: 0;\r\n\r\n        th {\r\n          background-color: var.$black;\r\n          font-size: 1.25rem;\r\n          font-weight: 600;\r\n          color: var.$white;\r\n          border-radius: 0;\r\n          border-top: 0;\r\n\r\n          @media (max-width: 1199px) {\r\n            font-size: 1rem;\r\n          }\r\n\r\n          @media (max-width: 767px) {\r\n            font-size: 0.875rem;\r\n          }\r\n\r\n          &:first-child {\r\n            border-top-left-radius: 2rem;\r\n            border-left: 0;\r\n          }\r\n\r\n          &:last-child {\r\n            border-top-right-radius: 2rem;\r\n            border-right: 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n    tbody {\r\n      tr {\r\n        &:last-child {\r\n          border-bottom: 0;\r\n\r\n          td {\r\n            border-bottom: 0;\r\n          }\r\n        }\r\n      }\r\n\r\n      td {\r\n        background: transparent;\r\n        font-size: 1.125rem;\r\n        font-weight: 600;\r\n        color: var.$clre9e9e9;\r\n\r\n        @media (max-width: 1199px) {\r\n          font-size: 1rem;\r\n        }\r\n\r\n        @media (max-width: 767px) {\r\n          font-size: 0.875rem;\r\n        }\r\n\r\n        a {\r\n          color: var.$baseclr;\r\n        }\r\n\r\n        &:first-child {\r\n          border-left: 0;\r\n        }\r\n\r\n        &:last-child {\r\n          border-right: 0;\r\n        }\r\n      }\r\n\r\n      tr {\r\n        &.no_record {\r\n          td {\r\n            padding: 1rem 0;\r\n          }\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.no_record_box {\r\n  padding: 3.125rem 1rem;\r\n  text-align: center;\r\n\r\n  svg {\r\n    opacity: 1;\r\n\r\n    path {\r\n      fill: var.$white;\r\n    }\r\n  }\r\n\r\n  img {\r\n    width: 150px !important;\r\n  }\r\n\r\n  h4 {\r\n    font-size: 1rem;\r\n    font-weight: 600;\r\n    margin-top: 0.625rem;\r\n    color: var.$white;\r\n\r\n    @media (max-width: 1679px) {\r\n      font-size: 0.875rem;\r\n    }\r\n  }\r\n\r\n  @media (max-width: 991px) {\r\n    padding: 2.5rem 1.25rem;\r\n  }\r\n}"], "names": [], "mappings": "AAGA;;;;AADA;;;;;;;AAME;;;;;;;;;;;;;;;AAkBI;;;;;;;AAOE;EAPF;;;;;AAWE;;;;AAKF;;;;AAKF;;;;AAGE;;;;;;AAIE;;;;;;;;;AAQE;EARF;;;;;AAYE;EAZF;;;;;AAgBE;;;;;AAKA;;;;;AAUF;;;;AASF;;;;;;;AAME;EANF;;;;;AAUE;EAVF;;;;;AAcE;;;;AAIA;;;;AAIA;;;;AAOE;;;;AASV;;;;;AAIE;;;;AAGE;;;;AAKF;;;;AAIA;;;;;;;AAME;EANF;;;;;AAWA;EA3BF", "debugId": null}}]}