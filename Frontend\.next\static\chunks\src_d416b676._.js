(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/utils/inactivityHandler.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
const MAX_INACTIVITY_DURATION = 30 * 60 * 1000;
const InactivityHandler = ()=>{
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const timeoutRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const [hasLoggedOut, setHasLoggedOut] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const logoutUser = ()=>{
        if (hasLoggedOut) return;
        setHasLoggedOut(true);
        const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("authToken");
        const shouldSetSessionExpired = !!token;
        // Clear cookies and storage first
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove("authToken");
        localStorage.removeItem("lastActivity");
        if (shouldSetSessionExpired) {
            sessionStorage.setItem("sessionExpired", "true");
        }
        localStorage.setItem("loggedOut", Date.now());
        // Then call the API
        fetch("/api/logout", {
            method: "POST",
            headers: {
                "Content-Type": "application/json"
            }
        }).finally(()=>{
            router.replace("/login");
        });
    };
    const updateLastActivity = ()=>{
        const now = Date.now();
        localStorage.setItem("lastActivity", now.toString());
        if (timeoutRef.current) clearTimeout(timeoutRef.current);
        timeoutRef.current = setTimeout(logoutUser, MAX_INACTIVITY_DURATION);
    };
    const checkInactivity = ()=>{
        const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("authToken");
        // If no token, don't start inactivity timer
        if (!token) return;
        const lastActivity = parseInt(localStorage.getItem("lastActivity") || "0", 10);
        const now = Date.now();
        if (!lastActivity || now - lastActivity > MAX_INACTIVITY_DURATION) {
            logoutUser();
        } else {
            updateLastActivity();
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "InactivityHandler.useEffect": ()=>{
            const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("authToken");
            // Only monitor inactivity if user is logged in
            if (!token) return;
            checkInactivity();
            const handleActivity = {
                "InactivityHandler.useEffect.handleActivity": ()=>updateLastActivity()
            }["InactivityHandler.useEffect.handleActivity"];
            const activityEvents = [
                "mousemove",
                "keydown",
                "click",
                "scroll"
            ];
            activityEvents.forEach({
                "InactivityHandler.useEffect": (event)=>window.addEventListener(event, handleActivity)
            }["InactivityHandler.useEffect"]);
            const handleStorage = {
                "InactivityHandler.useEffect.handleStorage": (e)=>{
                    if (e.key === "loggedOut") {
                        logoutUser();
                    }
                }
            }["InactivityHandler.useEffect.handleStorage"];
            window.addEventListener("storage", handleStorage);
            return ({
                "InactivityHandler.useEffect": ()=>{
                    activityEvents.forEach({
                        "InactivityHandler.useEffect": (event)=>window.removeEventListener(event, handleActivity)
                    }["InactivityHandler.useEffect"]);
                    window.removeEventListener("storage", handleStorage);
                    if (timeoutRef.current) clearTimeout(timeoutRef.current);
                }
            })["InactivityHandler.useEffect"];
        }
    }["InactivityHandler.useEffect"], []);
    return null;
};
_s(InactivityHandler, "RQtuVys52vgGLziNtbIa3hNIG78=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
_c = InactivityHandler;
const __TURBOPACK__default__export__ = InactivityHandler;
var _c;
__turbopack_context__.k.register(_c, "InactivityHandler");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/auth.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "createUsername": ()=>createUsername,
    "forgotPassword": ()=>forgotPassword,
    "getUser": ()=>getUser,
    "login": ()=>login,
    "logout": ()=>logout,
    "register": ()=>register,
    "resendVerificationCode": ()=>resendVerificationCode,
    "resetPassword": ()=>resetPassword,
    "verifyEmailToken": ()=>verifyEmailToken
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
;
;
;
const Url = ("TURBOPACK compile-time value", "http://127.0.0.1:8000");
const apiUrl = "".concat(Url, "/api/v1/auth");
const register = async (userData)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("".concat(apiUrl, "/register"), userData, {
            headers: {
                "Content-Type": "application/json"
            },
            withCredentials: true
        });
        return response.data; // Return success response
    } catch (error) {
        var _error_response;
        console.log(error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.data); // Log full response
        if (error.response && error.response.status === 422) {
            return {
                success: false,
                errors: error.response.data.errors || {},
                message: error.response.data.message
            };
        }
        return {
            success: false,
            errors: {
                general: "Something went wrong. Please try again."
            }
        };
    }
};
const resendVerificationCode = async (payload)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("".concat(apiUrl, "/resend-verification-code"), payload);
        return response.data;
    } catch (error) {
        var _error_response_data, _error_response;
        return {
            errors: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.errors) || {}
        };
    }
};
const verifyEmailToken = async (payload)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("".concat(apiUrl, "/verify-token"), {
            params: payload,
            headers: {
                "Content-Type": "application/json"
            }
        });
        return response.data;
    } catch (error) {
        var _error_response_data, _error_response;
        return {
            success: false,
            message: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || "Invalid token"
        };
    }
};
const createUsername = async (payload)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("".concat(apiUrl, "/create-username"), payload);
        return response.data;
    } catch (error) {
        return {
            error
        };
    }
};
const forgotPassword = async (type, value, uuid)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("".concat(apiUrl, "/forgot-password"), {
            type,
            value,
            uuid
        });
        return response.data;
    } catch (error) {
        if (error.response) {
            return {
                success: false,
                message: error.response.data.message || "Failed to send password reset link"
            };
        }
        return {
            success: false,
            message: "Something went wrong. Please try again later."
        };
    }
};
const resetPassword = async (newPassword)=>{
    try {
        const resetPasswordData = JSON.parse(sessionStorage.getItem("reset_password_data"));
        if (!resetPasswordData || !resetPasswordData.uuid) {
            throw new Error("No reset data found");
        }
        const { uuid } = resetPasswordData;
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("".concat(apiUrl, "/reset-password"), {
            uuid,
            new_password: newPassword
        });
        return response.data;
    } catch (error) {
        var _error_response_data, _error_response;
        console.error("Error resetting password", error);
        return {
            success: false,
            message: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || "Error resetting password"
        };
    }
};
const login = async (credentials)=>{
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("".concat(Url, "/sanctum/csrf-cookie"), {
            withCredentials: true
        });
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("".concat(apiUrl, "/login"), credentials, {
            headers: {
                "Content-Type": "application/json"
            },
            withCredentials: true
        });
        localStorage.setItem("lastActivity", Date.now().toString());
        console.log('auth.js response.data', response.data);
        return response.data;
    } catch (error) {
        var _error_response_data, _error_response, _error_response_data1, _error_response1, _error_response_data2, _error_response2, _error_response_data3, _error_response3, _error_response_data4, _error_response4, _error_response_data5, _error_response5;
        console.error("Login error authjs:", error.response);
        return {
            success: false,
            message: ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || "Invalid credentials",
            errors: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.errors) || [],
            captcha_required: ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data2 = _error_response2.data) === null || _error_response_data2 === void 0 ? void 0 : _error_response_data2.type) === 'captcha' ? (_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : (_error_response_data3 = _error_response3.data) === null || _error_response_data3 === void 0 ? void 0 : _error_response_data3.state : false,
            lockout_redirect: ((_error_response4 = error.response) === null || _error_response4 === void 0 ? void 0 : (_error_response_data4 = _error_response4.data) === null || _error_response_data4 === void 0 ? void 0 : _error_response_data4.type) === 'redirect' ? (_error_response5 = error.response) === null || _error_response5 === void 0 ? void 0 : (_error_response_data5 = _error_response5.data) === null || _error_response_data5 === void 0 ? void 0 : _error_response_data5.state : false,
            redirect_to: "/locate-account"
        };
    }
};
const logout = async ()=>{
    try {
        await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post("".concat(apiUrl, "/logout"), {}, {
            headers: {
                Authorization: "Bearer ".concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("authToken"))
            }
        });
        return true;
    } catch (error) {
        return false;
    }
};
const getUser = async ()=>{
    const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('authToken');
    if (!token) return null;
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("".concat(apiUrl, "/user"), {
            headers: {
                Authorization: "Bearer ".concat(token)
            }
        });
        return response.data;
    } catch (error) {
        console.error('Error fetching user data:', error);
        throw error;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/remember2FAUtils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "cleanupExpiredRemember2FACookie": ()=>cleanupExpiredRemember2FACookie,
    "getRemember2FACookie": ()=>getRemember2FACookie,
    "getRemember2FACookieName": ()=>getRemember2FACookieName,
    "getRemember2FAValidationStatus": ()=>getRemember2FAValidationStatus,
    "hasRemember2FACookie": ()=>hasRemember2FACookie,
    "isRemember2FACookieValid": ()=>isRemember2FACookieValid,
    "removeRemember2FACookie": ()=>removeRemember2FACookie
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
;
const getRemember2FACookieName = ()=>{
    return 'remember_2fa';
};
const hasRemember2FACookie = ()=>{
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    const cookieName = getRemember2FACookieName();
    const cookieValue = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(cookieName);
    return !!cookieValue;
};
const getRemember2FACookie = ()=>{
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    const cookieName = getRemember2FACookieName();
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(cookieName) || null;
};
const isRemember2FACookieValid = ()=>{
    try {
        const cookieValue = getRemember2FACookie();
        if (!cookieValue) {
            return false;
        }
        // Check if we're in browser environment
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
        // Basic structure validation - check if it's base64 encoded JSON
        const decoded = atob(cookieValue);
        const cookieData = JSON.parse(decoded);
        // Check if it has the expected structure
        if (!cookieData || !cookieData.data || !cookieData.signature) {
            return false;
        }
        // Parse the inner data
        const innerData = JSON.parse(cookieData.data);
        // Check if it has required fields
        if (!innerData || !innerData.user_id || !innerData.timestamp || !innerData.type) {
            return false;
        }
        // Check if it's the right type
        if (innerData.type !== 'remember_2fa') {
            return false;
        }
        // Basic expiration check (30 days)
        const timestamp = new Date(innerData.timestamp);
        const now = new Date();
        const daysDiff = (now - timestamp) / (1000 * 60 * 60 * 24);
        if (daysDiff > 30) {
            console.log('Remember 2FA cookie expired (client-side check)');
            return false;
        }
        return true;
    } catch (error) {
        console.warn('Error validating remember 2FA cookie:', error);
        return false;
    }
};
const removeRemember2FACookie = ()=>{
    if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
    ;
    const cookieName = getRemember2FACookieName();
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove(cookieName);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove(cookieName, {
        path: '/'
    }); // Also try with explicit path
};
const getRemember2FAValidationStatus = ()=>{
    const cookieValue = getRemember2FACookie();
    if (!cookieValue) {
        return {
            status: 'missing',
            cookieValue: null,
            isValid: false
        };
    }
    const isValid = isRemember2FACookieValid();
    return {
        status: isValid ? 'valid' : 'invalid',
        cookieValue: cookieValue,
        isValid: isValid
    };
};
const cleanupExpiredRemember2FACookie = ()=>{
    if (hasRemember2FACookie() && !isRemember2FACookieValid()) {
        console.log('Removing expired remember 2FA cookie');
        removeRemember2FACookie();
        return true; // Cookie was removed
    }
    return false; // No action taken
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/redux/authSlice.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "getUser": ()=>getUser,
    "loginUser": ()=>loginUser,
    "logoutUser": ()=>logoutUser,
    "setUser": ()=>setUser
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/auth.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$remember2FAUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/remember2FAUtils.js [app-client] (ecmascript)");
;
;
;
;
;
const loginUser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createAsyncThunk"])("auth/login", async (param, param1)=>{
    let { email, password, captchaToken } = param, { rejectWithValue } = param1;
    try {
        var _response_data;
        // Check for remember 2FA cookie before login
        const remember2FAStatus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$remember2FAUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getRemember2FAValidationStatus"])();
        const credentials = {
            email,
            password,
            captchaToken,
            remember_2fa_status: remember2FAStatus.status,
            remember_2fa_cookie: remember2FAStatus.cookieValue
        };
        const response = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$auth$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["login"])(credentials);
        // console.log('slice response',response)
        // console.log('slice response.data',response.data)
        // if (response?.user) {
        //   localStorage.setItem("user", JSON.stringify(response.user));
        //   Cookies.set("authToken", response.token, { expires: 1 });
        // }
        // if (response?.captcha_required) {
        //   sessionStorage.setItem("captcha_required", "true");
        // } else {
        //   sessionStorage.removeItem("captcha_required");
        // }
        if (response === null || response === void 0 ? void 0 : (_response_data = response.data) === null || _response_data === void 0 ? void 0 : _response_data.user) {
            var _response_data1;
            // Only set auth token if 2FA is not required
            if (!(response === null || response === void 0 ? void 0 : (_response_data1 = response.data) === null || _response_data1 === void 0 ? void 0 : _response_data1.requires_2fa)) {
                var _response_data2;
                localStorage.setItem("user", JSON.stringify(response.data.user));
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set("authToken", response.data.token);
                if (response === null || response === void 0 ? void 0 : (_response_data2 = response.data) === null || _response_data2 === void 0 ? void 0 : _response_data2.active_subscription) {
                    var _response_data3;
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set("active_subscription", response === null || response === void 0 ? void 0 : (_response_data3 = response.data) === null || _response_data3 === void 0 ? void 0 : _response_data3.active_subscription, {
                        expires: 1
                    });
                }
            } else {
                // For 2FA flow, store user data but not the auth token yet
                localStorage.setItem("user", JSON.stringify(response.data.user));
                // Store temporary token for 2FA verification
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].set("tempAuthToken", response.data.token);
            }
        // const subscriptionId = response.data.user.subscription_id;
        // if (subscriptionId) {
        //   Cookies.set("subscription_id", subscriptionId, { expires: 1 });
        // }
        }
        if (response === null || response === void 0 ? void 0 : response.captcha_required) {
            sessionStorage.setItem("captcha_required", "true");
        } else {
            sessionStorage.removeItem("captcha_required");
        }
        if (!response.success) {
            return rejectWithValue(response);
        }
        return response;
    } catch (error) {
        var _error_response_data, _error_response, _error_response_data1, _error_response1, _error_response_data2, _error_response2, _error_response_data3, _error_response3;
        console.error("Login error message authslice:", error.response);
        const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || "An error occurred.";
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].error(errorMessage);
        return rejectWithValue({
            success: false,
            message: errorMessage,
            errors: ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.errors) || [],
            captcha_required: ((_error_response2 = error.response) === null || _error_response2 === void 0 ? void 0 : (_error_response_data2 = _error_response2.data) === null || _error_response_data2 === void 0 ? void 0 : _error_response_data2.type) === 'captcha' ? (_error_response3 = error.response) === null || _error_response3 === void 0 ? void 0 : (_error_response_data3 = _error_response3.data) === null || _error_response_data3 === void 0 ? void 0 : _error_response_data3.state : false
        });
    }
});
const authSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "auth",
    initialState: {
        user: null,
        token: null,
        loading: false,
        error: null
    },
    reducers: {
        logoutUser: (state)=>{
            state.user = null;
            state.token = null;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove("authToken");
            localStorage.clear();
            sessionStorage.clear();
        },
        setUser: (state, action)=>{
            state.user = action.payload;
        }
    },
    extraReducers: (builder)=>{
        builder.addCase(loginUser.pending, (state)=>{
            state.loading = true;
            state.error = null;
        }).addCase(loginUser.fulfilled, (state, action)=>{
            var _action_payload_data, _action_payload;
            state.loading = false;
            // Only set user and token if 2FA is not required
            if (!((_action_payload = action.payload) === null || _action_payload === void 0 ? void 0 : (_action_payload_data = _action_payload.data) === null || _action_payload_data === void 0 ? void 0 : _action_payload_data.requires_2fa)) {
                var _action_payload_data1, _action_payload1, _action_payload_data2, _action_payload2;
                state.user = ((_action_payload1 = action.payload) === null || _action_payload1 === void 0 ? void 0 : (_action_payload_data1 = _action_payload1.data) === null || _action_payload_data1 === void 0 ? void 0 : _action_payload_data1.user) || action.payload.user;
                state.token = ((_action_payload2 = action.payload) === null || _action_payload2 === void 0 ? void 0 : (_action_payload_data2 = _action_payload2.data) === null || _action_payload_data2 === void 0 ? void 0 : _action_payload_data2.token) || action.payload.token;
            }
        // For 2FA responses, don't set the user/token in Redux state yet
        }).addCase(loginUser.rejected, (state, action)=>{
            state.loading = false;
            state.error = action.payload;
        });
    }
});
const { logoutUser, setUser } = authSlice.actions;
const getUser = (state)=>state.auth.user;
const __TURBOPACK__default__export__ = authSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/redux/store/authStore.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// src/redux/store/authStore.js
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$authSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/authSlice.js [app-client] (ecmascript)");
;
;
const authStore = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: {
        auth: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$authSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    }
});
const __TURBOPACK__default__export__ = authStore;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/Components/providers/Providers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>Providers
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2f$authStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/store/authStore.js [app-client] (ecmascript)");
"use client";
;
;
;
function Providers(param) {
    let { children } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Provider"], {
        store: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2f$authStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        children: children
    }, void 0, false, {
        fileName: "[project]/src/Components/providers/Providers.js",
        lineNumber: 7,
        columnNumber: 10
    }, this);
}
_c = Providers;
var _c;
__turbopack_context__.k.register(_c, "Providers");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/redux/metaSlice.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "setMeta": ()=>setMeta
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
;
const initialState = {
    title: "Default Title",
    description: "Default description",
    keywords: "default, keywords"
};
const metaSlice = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["createSlice"])({
    name: "meta",
    initialState,
    reducers: {
        setMeta: (state, action)=>{
            return {
                ...state,
                ...action.payload
            };
        }
    }
});
const { setMeta } = metaSlice.actions;
const __TURBOPACK__default__export__ = metaSlice.reducer;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/redux/store/metaStore.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/@reduxjs/toolkit/dist/redux-toolkit.modern.mjs [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$metaSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/metaSlice.js [app-client] (ecmascript)");
;
;
const store = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$reduxjs$2f$toolkit$2f$dist$2f$redux$2d$toolkit$2e$modern$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["configureStore"])({
    reducer: {
        meta: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$metaSlice$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    }
});
const __TURBOPACK__default__export__ = store;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/Components/providers/MetaProvider.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>MetaProvider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-redux/dist/react-redux.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2f$metaStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/redux/store/metaStore.js [app-client] (ecmascript)");
"use client";
;
;
;
function MetaProvider(param) {
    let { children } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$redux$2f$dist$2f$react$2d$redux$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Provider"], {
        store: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$redux$2f$store$2f$metaStore$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        children: children
    }, void 0, false, {
        fileName: "[project]/src/Components/providers/MetaProvider.js",
        lineNumber: 7,
        columnNumber: 10
    }, this);
}
_c = MetaProvider;
var _c;
__turbopack_context__.k.register(_c, "MetaProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/i18n.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2f$dist$2f$esm$2f$i18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/i18next/dist/esm/i18next.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-i18next/dist/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$initReactI18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-i18next/dist/es/initReactI18next.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2d$http$2d$backend$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/i18next-http-backend/esm/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2d$browser$2d$languagedetector$2f$dist$2f$esm$2f$i18nextBrowserLanguageDetector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/i18next-browser-languagedetector/dist/esm/i18nextBrowserLanguageDetector.js [app-client] (ecmascript)");
;
;
;
;
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2f$dist$2f$esm$2f$i18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].use(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2d$http$2d$backend$2f$esm$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]).use(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2d$browser$2d$languagedetector$2f$dist$2f$esm$2f$i18nextBrowserLanguageDetector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]).use(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$initReactI18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initReactI18next"]).init({
    fallbackLng: 'en',
    debug: false,
    supportedLngs: [
        'en',
        'fr'
    ],
    backend: {
        loadPath: '/locales/{{lng}}.json'
    },
    interpolation: {
        escapeValue: false
    }
});
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2f$dist$2f$esm$2f$i18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/providers/I18nProvider.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-i18next/dist/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$I18nextProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-i18next/dist/es/I18nextProvider.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/i18n.js [app-client] (ecmascript)");
'use client';
;
;
;
const I18nProvider = (param)=>{
    let { children } = param;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$I18nextProvider$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["I18nextProvider"], {
        i18n: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$i18n$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        children: children
    }, void 0, false, {
        fileName: "[project]/src/providers/I18nProvider.js",
        lineNumber: 8,
        columnNumber: 10
    }, ("TURBOPACK compile-time value", void 0));
};
_c = I18nProvider;
const __TURBOPACK__default__export__ = I18nProvider;
var _c;
__turbopack_context__.k.register(_c, "I18nProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/context/LanguageContext.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "LanguageProvider": ()=>LanguageProvider,
    "useLanguage": ()=>useLanguage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2f$dist$2f$esm$2f$i18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/i18next/dist/esm/i18next.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client"; // Required for Next.js client-side state
;
;
// Create Context
const LanguageContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(null);
const LanguageProvider = (param)=>{
    let { children } = param;
    _s();
    const [language, setLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("en");
    const changeLanguage = (lang)=>{
        setLanguage(lang); // ✅ This updates state
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$i18next$2f$dist$2f$esm$2f$i18next$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].changeLanguage(lang);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(LanguageContext.Provider, {
        value: {
            language,
            changeLanguage
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/context/LanguageContext.js",
        lineNumber: 19,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
_s(LanguageProvider, "JgNS4s3wc06/6u6z+Ak7Ai5ELN8=");
_c = LanguageProvider;
const useLanguage = ()=>{
    _s1();
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(LanguageContext);
    if (!context) {
        throw new Error("useLanguage must be used within a LanguageProvider");
    }
    return context;
};
_s1(useLanguage, "b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=");
var _c;
__turbopack_context__.k.register(_c, "LanguageProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/lib/useTranslation.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "t": ()=>t,
    "useSetLanguage": ()=>useSetLanguage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/react-i18next/dist/es/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$useTranslation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-i18next/dist/es/useTranslation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$LanguageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/context/LanguageContext.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature();
"use client"; // Since hooks use client-side state
;
;
const t = (key)=>{
    _s();
    const { t } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$useTranslation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslation"])();
    return t(key) || key; // Return key if translation is missing
};
_s(t, "zlIdU9EjM2llFt74AbE2KsUJXyM=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$i18next$2f$dist$2f$es$2f$useTranslation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useTranslation"]
    ];
});
const useSetLanguage = ()=>{
    _s1();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$LanguageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLanguage"])(); // Returns { language, changeLanguage }
};
_s1(useSetLanguage, "DlB0VrZK86f5u4V+gaqgmmVL1+0=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$context$2f$LanguageContext$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLanguage"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/Components/ClientSideCanonicalTag.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const ClientSideCanonicalTag = ()=>{
    _s();
    const environment = ("TURBOPACK compile-time value", "dev");
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const keyValue = searchParams === null || searchParams === void 0 ? void 0 : searchParams.get('key');
    const isSearchPage = keyValue !== null && keyValue.trim() !== '';
    const hasKeyParam = keyValue !== null && keyValue.trim() !== '';
    // const baseUrl = environment === 'dev' ? 'https://dev.tradereply.com' : 'https://www.tradereply.com';
    // const pageParam = searchParams?.get('page');
    // const currentPage = pageParam ? parseInt(pageParam, 10) : 1;
    // const canonicalUrl = `${baseUrl}${pathname}`
    // const prevPage = currentPage > 1 ? `${baseUrl}${pathname}?page=${currentPage - 1}` : null;
    // const nextPage = `${baseUrl}${pathname}?page=${currentPage + 1}`;
    const baseUrl = ("TURBOPACK compile-time truthy", 1) ? 'https://dev.tradereply.com' : "TURBOPACK unreachable";
    // Extract current page from pathname (e.g. /education/page/3)
    const pathMatch = pathname === null || pathname === void 0 ? void 0 : pathname.match(/\/page\/(\d+)$/);
    const currentPage = pathMatch ? parseInt(pathMatch[1], 10) : 1;
    // Base path without /page/X
    const basePath = pathname.replace(/\/page\/\d+$/, '');
    // Canonical
    const canonicalUrl = currentPage === 1 ? "".concat(baseUrl).concat(basePath) : "".concat(baseUrl).concat(basePath, "/page/").concat(currentPage);
    // Prev
    const prevPage = currentPage > 1 ? currentPage === 2 ? "".concat(baseUrl).concat(basePath) : "".concat(baseUrl).concat(basePath, "/page/").concat(currentPage - 1) : null;
    // Next (you can conditionally hide this if you know totalPages)
    const nextPage = "".concat(baseUrl).concat(basePath, "/page/").concat(currentPage + 1);
    const isCategoryPage = pathname === null || pathname === void 0 ? void 0 : pathname.startsWith('/category');
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
        children: isSearchPage ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
            name: "robots",
            content: "noindex"
        }, void 0, false, {
            fileName: "[project]/src/Components/ClientSideCanonicalTag.js",
            lineNumber: 77,
            columnNumber: 9
        }, ("TURBOPACK compile-time value", void 0)) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("meta", {
                    property: "og:url",
                    content: canonicalUrl
                }, void 0, false, {
                    fileName: "[project]/src/Components/ClientSideCanonicalTag.js",
                    lineNumber: 81,
                    columnNumber: 11
                }, ("TURBOPACK compile-time value", void 0)),
                prevPage && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("link", {
                    rel: "prev",
                    href: prevPage
                }, void 0, false, {
                    fileName: "[project]/src/Components/ClientSideCanonicalTag.js",
                    lineNumber: 82,
                    columnNumber: 24
                }, ("TURBOPACK compile-time value", void 0))
            ]
        }, void 0, true)
    }, void 0, false);
};
_s(ClientSideCanonicalTag, "AxA9T5G2Po78UC4hL8ljCdvMciE=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"]
    ];
});
_c = ClientSideCanonicalTag;
const __TURBOPACK__default__export__ = ClientSideCanonicalTag;
var _c;
__turbopack_context__.k.register(_c, "ClientSideCanonicalTag");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/axiosInstance.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$build$2f$polyfills$2f$process$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = /*#__PURE__*/ __turbopack_context__.i("[project]/node_modules/next/dist/build/polyfills/process.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$react$2d$hot$2d$toast$2f$dist$2f$index$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/react-hot-toast/dist/index.mjs [app-client] (ecmascript)");
;
;
;
const axiosInstance = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].create({
    baseURL: "".concat(("TURBOPACK compile-time value", "http://127.0.0.1:8000"), "/api/v1/"),
    withCredentials: true,
    headers: {
        "Accept": "application/json"
    }
});
// Flag to prevent multiple simultaneous logout redirects
let isRedirecting = false;
axiosInstance.interceptors.request.use((config)=>{
    const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("authToken"); // Fetch latest token
    if (token) {
        config.headers.Authorization = "Bearer ".concat(token);
    }
    return config;
}, (error)=>Promise.reject(error));
// Response Interceptor: Handle Unauthorized Errors
axiosInstance.interceptors.response.use((response)=>response, (error)=>{
    if (error.response) {
        const { status } = error.response;
        if (status === 401) {
            handleUnauthorizedAccess();
        } else if (status === 404) {
            console.error('Resource not found!');
        } else if (status >= 500) {
            console.error('Server error! Please try again later.');
        }
    } else {
        console.error('Network error or request timeout.');
    }
    return Promise.reject(error);
});
/**
 * Handle unauthorized access with proper cleanup and redirect
 */ function handleUnauthorizedAccess() {
    // Prevent multiple simultaneous redirects
    if (isRedirecting) {
        return;
    }
    isRedirecting = true;
    console.log('Unauthorized access detected. Logging out...');
    // Clear all authentication data
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].remove("authToken");
    localStorage.removeItem("user");
    localStorage.removeItem("lastActivity");
    localStorage.setItem("loggedOut", Date.now());
    sessionStorage.clear();
    // Show user notification
    // toast.error('Your session has expired. Please log in again.', {
    //   duration: 4000,
    //   position: 'top-center',
    // });
    // Use window.location for redirect (most reliable method)
    if ("TURBOPACK compile-time truthy", 1) {
        // Check if we're already on login page to prevent redirect loop
        if (window.location.pathname !== '/login') {
            window.location.href = '/login';
        }
    }
    // Reset redirect flag after a delay
    setTimeout(()=>{
        isRedirecting = false;
    }, 1000);
}
const __TURBOPACK__default__export__ = axiosInstance;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/apiUtils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "changePassword": ()=>changePassword,
    "checkUsernameAvailability": ()=>checkUsernameAvailability,
    "deleteRequest": ()=>deleteRequest,
    "disable2FA": ()=>disable2FA,
    "enable2FA": ()=>enable2FA,
    "generateRestoreCode": ()=>generateRestoreCode,
    "get": ()=>get,
    "get2FAStatus": ()=>get2FAStatus,
    "getBrokers": ()=>getBrokers,
    "getSecretQuestions": ()=>getSecretQuestions,
    "post": ()=>post,
    "put": ()=>put,
    "saveSecretQuestions": ()=>saveSecretQuestions,
    "update2FAAlwaysRequired": ()=>update2FAAlwaysRequired,
    "updateSecretQuestions": ()=>updateSecretQuestions,
    "updateUsername": ()=>updateUsername,
    "verifyRestoreCode": ()=>verifyRestoreCode
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/axiosInstance.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/axios/lib/axios.js [app-client] (ecmascript)");
;
;
const get = async function(url) {
    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(url, {
            params
        });
        return response.data;
    } catch (error) {
        if (error.name === 'AbortError' || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$axios$2f$lib$2f$axios$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isCancel(error)) {
            console.log('Request aborted:', url); // Optional quiet log
            return null; // Or throw if needed
        }
        console.error('Error with GET request:', error);
        throw error;
    }
};
const getBrokers = async function(url) {
    let params = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get(url, {
            params
        });
        return response.data;
    } catch (error) {
        console.error('Error with GET request:', error);
        throw error; // Re-throw for further handling
    }
};
const post = async (url, data)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post(url, data);
        return response.data;
    } catch (error) {
        console.error('Error with POST request:', error);
        throw error;
    }
};
const put = async (url, data)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put(url, data);
        return response.data;
    } catch (error) {
        console.error('Error with PUT request:', error);
        throw error;
    }
};
const deleteRequest = async (url)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].delete(url);
        return response.data;
    } catch (error) {
        console.error('Error with DELETE request:', error);
        throw error;
    }
};
const updateUsername = async (username)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/username/update', {
            username
        });
        return response.data;
    } catch (error) {
        console.error('Error updating username:', error);
        throw error;
    }
};
const checkUsernameAvailability = async (username)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/username/check-availability', {
            username
        });
        return response.data;
    } catch (error) {
        console.error('Error checking username availability:', error);
        throw error;
    }
};
const get2FAStatus = async ()=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/2fa/status');
        return response.data;
    } catch (error) {
        console.error('Error fetching 2FA status:', error);
        throw error;
    }
};
const enable2FA = async ()=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/2fa/enable');
        return response.data;
    } catch (error) {
        console.error('Error enabling 2FA:', error);
        throw error;
    }
};
const disable2FA = async ()=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/2fa/disable');
        return response.data;
    } catch (error) {
        console.error('Error disabling 2FA:', error);
        throw error;
    }
};
const generateRestoreCode = async ()=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/2fa/generate-restore-code');
        return response.data;
    } catch (error) {
        console.error('Error generating restore code:', error);
        throw error;
    }
};
const update2FAAlwaysRequired = async (alwaysRequired)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/2fa/update-always-required', {
            always_required: alwaysRequired
        });
        return response.data;
    } catch (error) {
        console.error('Error updating 2FA always required setting:', error);
        throw error;
    }
};
const verifyRestoreCode = async (restoreCode, sessionId)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/2fa/verify-restore-code', {
            restore_code: restoreCode,
            session_id: sessionId
        });
        return response.data;
    } catch (error) {
        console.error('Error verifying restore code:', error);
        throw error;
    }
};
const getSecretQuestions = async ()=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get('/account/secret-questions');
        return response.data;
    } catch (error) {
        console.error('Error fetching secret questions:', error);
        throw error;
    }
};
const saveSecretQuestions = async (questions)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/account/secret-questions', {
            questions
        });
        return response.data;
    } catch (error) {
        console.error('Error saving secret questions:', error);
        throw error;
    }
};
const updateSecretQuestions = async (questions)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].put('/account/secret-questions', {
            questions
        });
        return response.data;
    } catch (error) {
        console.error('Error updating secret questions:', error);
        throw error;
    }
};
const changePassword = async (passwordData)=>{
    try {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$axiosInstance$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].post('/account/change-password', passwordData);
        return response.data;
    } catch (error) {
        console.error('Error changing password:', error);
        throw error;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/Hooks/useAuthHeartbeat.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>useAuthHeartbeat
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/apiUtils.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function useAuthHeartbeat() {
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const intervalRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const isActiveRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(true);
    const lastHeartbeatRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(Date.now());
    // Configuration
    const HEARTBEAT_INTERVAL = 1000; // 1 second for near-instant detection
    const MAX_RETRY_ATTEMPTS = 3;
    const RETRY_DELAY = 5000; // 5 seconds
    /**
   * Perform heartbeat check
   */ const performHeartbeat = async ()=>{
        try {
            // Only perform heartbeat if user has auth token
            const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("authToken");
            if (!token) {
                stopHeartbeat();
                return;
            }
            // Only perform heartbeat if tab is active and user is active
            if (!isActiveRef.current || document.hidden) {
                return;
            }
            // Make lightweight API call to verify token
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$apiUtils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["get"])('auth/heartbeat');
            // Update last successful heartbeat
            lastHeartbeatRef.current = Date.now();
        } catch (error) {
            var _error_response;
            // 401 errors are handled by axios interceptor
            // Other errors we can log but don't need to handle specially
            if ((error === null || error === void 0 ? void 0 : (_error_response = error.response) === null || _error_response === void 0 ? void 0 : _error_response.status) !== 401) {
                console.warn('Heartbeat failed:', error.message);
            }
        }
    };
    /**
   * Start heartbeat monitoring
   */ const startHeartbeat = ()=>{
        // Don't start if already running
        if (intervalRef.current) {
            return;
        }
        // Only start if user has auth token
        const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("authToken");
        if (!token) {
            return;
        }
        // Heartbeat monitoring started
        // Perform initial heartbeat
        performHeartbeat();
        // Set up interval
        intervalRef.current = setInterval(performHeartbeat, HEARTBEAT_INTERVAL);
    };
    /**
   * Stop heartbeat monitoring
   */ const stopHeartbeat = ()=>{
        if (intervalRef.current) {
            clearInterval(intervalRef.current);
            intervalRef.current = null;
        }
    };
    /**
   * Handle visibility change (tab focus/blur)
   */ const handleVisibilityChange = ()=>{
        if (document.hidden) {
            // Tab is hidden, reduce activity
            isActiveRef.current = false;
        } else {
            // Tab is visible, resume activity
            isActiveRef.current = true;
            // If it's been a while since last heartbeat, perform one immediately
            const timeSinceLastHeartbeat = Date.now() - lastHeartbeatRef.current;
            if (timeSinceLastHeartbeat > HEARTBEAT_INTERVAL) {
                performHeartbeat();
            }
        }
    };
    /**
   * Handle user activity (mouse, keyboard, etc.)
   * Trigger immediate heartbeat on user activity for faster detection
   */ const handleUserActivity = ()=>{
        isActiveRef.current = true;
        localStorage.setItem("lastActivity", Date.now().toString());
        // Trigger immediate heartbeat on user activity for faster logout detection
        const timeSinceLastHeartbeat = Date.now() - lastHeartbeatRef.current;
        if (timeSinceLastHeartbeat > 500) {
            performHeartbeat();
        }
    };
    // Set up effect
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useAuthHeartbeat.useEffect": ()=>{
            // Start heartbeat monitoring
            startHeartbeat();
            // Listen for visibility changes
            document.addEventListener('visibilitychange', handleVisibilityChange);
            // Listen for window focus (when user switches back to tab)
            window.addEventListener('focus', performHeartbeat);
            // Listen for user activity (more events for faster detection)
            const activityEvents = [
                'mousedown',
                'mousemove',
                'keypress',
                'scroll',
                'touchstart',
                'click'
            ];
            activityEvents.forEach({
                "useAuthHeartbeat.useEffect": (event)=>{
                    document.addEventListener(event, handleUserActivity, {
                        passive: true
                    });
                }
            }["useAuthHeartbeat.useEffect"]);
            // Cleanup on unmount
            return ({
                "useAuthHeartbeat.useEffect": ()=>{
                    stopHeartbeat();
                    document.removeEventListener('visibilitychange', handleVisibilityChange);
                    window.removeEventListener('focus', performHeartbeat);
                    activityEvents.forEach({
                        "useAuthHeartbeat.useEffect": (event)=>{
                            document.removeEventListener(event, handleUserActivity);
                        }
                    }["useAuthHeartbeat.useEffect"]);
                }
            })["useAuthHeartbeat.useEffect"];
        }
    }["useAuthHeartbeat.useEffect"], []);
    // Return control functions (optional, for manual control)
    return {
        startHeartbeat,
        stopHeartbeat,
        performHeartbeat
    };
}
_s(useAuthHeartbeat, "y1dXO4U0fauxKwkTix9fOwmiGPw=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"]
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/Components/auth/AuthHeartbeatProvider.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": ()=>AuthHeartbeatProvider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/js-cookie/dist/js.cookie.mjs [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Hooks$2f$useAuthHeartbeat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/Hooks/useAuthHeartbeat.js [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
function AuthHeartbeatProvider(param) {
    let { children } = param;
    _s();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const { startHeartbeat, stopHeartbeat } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Hooks$2f$useAuthHeartbeat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    // Define routes that don't need heartbeat monitoring (public routes)
    const publicRoutes = [
        '/login',
        '/signup',
        '/forget',
        '/reset-password',
        '/verify-email',
        '/locate-account',
        '/auth',
        '/',
        '/blog',
        '/education',
        '/about',
        '/contact',
        '/privacy',
        '/terms',
        '/pricing'
    ];
    // Define routes that definitely need heartbeat monitoring (authenticated routes)
    const authenticatedRoutes = [
        '/dashboard',
        '/account',
        '/create-username',
        '/change-password',
        '/security-check'
    ];
    // Check if current route needs authentication monitoring
    const isAuthenticatedRoute = ()=>{
        // First check if it's explicitly an authenticated route
        if (authenticatedRoutes.some((route)=>pathname.startsWith(route))) {
            return true;
        }
        // If it's a public route, no monitoring needed
        if (publicRoutes.some((route)=>pathname.startsWith(route))) {
            return false;
        }
        // For other routes, check if user has auth token
        const token = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$js$2d$cookie$2f$dist$2f$js$2e$cookie$2e$mjs__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].get("authToken");
        return !!token;
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "AuthHeartbeatProvider.useEffect": ()=>{
            const isAuth = isAuthenticatedRoute();
            if (isAuth) {
                // Start heartbeat monitoring for authenticated routes
                startHeartbeat();
            } else {
                // Stop heartbeat monitoring for public routes
                stopHeartbeat();
            }
            // Cleanup on route change or unmount
            return ({
                "AuthHeartbeatProvider.useEffect": ()=>{
                // Don't stop heartbeat on cleanup unless we're going to a public route
                // This prevents stopping heartbeat when navigating between authenticated pages
                }
            })["AuthHeartbeatProvider.useEffect"];
        }
    }["AuthHeartbeatProvider.useEffect"], [
        pathname,
        startHeartbeat,
        stopHeartbeat
    ]);
    return children;
}
_s(AuthHeartbeatProvider, "RdvhJbImVvcZF+Nq4whoJKg7vqo=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$Hooks$2f$useAuthHeartbeat$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    ];
});
_c = AuthHeartbeatProvider;
var _c;
__turbopack_context__.k.register(_c, "AuthHeartbeatProvider");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_d416b676._.js.map