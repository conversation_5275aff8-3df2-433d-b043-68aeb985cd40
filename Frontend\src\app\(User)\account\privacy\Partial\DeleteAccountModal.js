import React from 'react'
import { WhiteCrossIcon } from "@/assets/svgIcons/SvgIcon";
import { Col, Row } from "react-bootstrap";

export default function DeleteAccountModal({ type, toggleModal, closeModal }) {
    let title = "";
    let body1 = "";
    let body2 = "";
    let buttonText = "";
    let CancelText = "";

    if (type === "delete") {
        title = "Permanently Delete Your Account?";
        body1 = "Your account and all associated data will be scheduled for deletion and permanently removed after 30 days. Until then, you may continue using your account or cancel the deletion request."
        body2 = "Some essential data (such as transaction records or messages to other users) may be retained to meet legal and platform obligations."
        buttonText = "Yes, Delete My Account";
        CancelText = "Cancel";
    } else if (type === "cancel") {
        title = "Cancel Account Deletion?";
        body1 = "This will cancel your scheduled deletion request and restore your account as normal.";
        buttonText = "Yes, Cancel Deletion";
        CancelText = "Keep Scheduled";
    }
    return (
        <div className="modal_overlay">
            <div className="modal-body">
                <div className='d-flex justify-content-between align-items-center mb-4'>
                    <h4 className='mb-0'>{title}</h4>
                    <button onClick={closeModal}>
                        <WhiteCrossIcon />
                    </button>
                </div>
                <div>
                    <p>{body1}</p>
                    <p>{body2}</p>
                </div>
                <Row className='justify-content-center mt-3'>
                    <Col lg={5} xs={12}>
                        <button className="btn-style white-btn w-100 modal-footer-btn" onClick={closeModal}>{CancelText}</button>
                    </Col>
                    <Col lg={5} xs={12} className='mt-xl-0 mt-3'>
                        <button className="btn-style w-100 modal-footer-btn" onClick={toggleModal}>{buttonText}</button>
                    </Col>
                </Row>
            </div>
        </div>
    )
}
