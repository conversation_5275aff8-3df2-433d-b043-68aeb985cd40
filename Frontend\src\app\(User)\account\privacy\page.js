'use client';
import { Col, Row } from "react-bootstrap";
import React from 'react'
import AccountLayout from "@/Layouts/AccountLayout";
import SidebarHeading from "@/Components/common/Account/SidebarHeading";
import MetaHead from "@/Seo/Meta/MetaHead";
import "@/css/account/PrivacyAccount.scss";
import Link from "next/link";
import PersonalRecommendations from "./Partial/PersonalRecommendations";
import CommunicationPreferences from "./Partial/CommunicationPreferences";
import DeleteMyAccount from "./Partial/DeleteMyAccount";

export default function Privacy() {
    const metaArray = {
        noindex: true,
        title: "Account Privacy | Control Your Data | TradeReply",
        description: "Control your privacy settings on TradeReply.com. Adjust how your data is shared and manage your communication preferences.",
        canonical_link: "https://www.tradereply.com/account/privacy",
        og_site_name: "TradeReply",
        og_title: "Privacy Settings | Control Your Data | TradeReply",
        og_description: "Control your privacy settings on TradeReply. Adjust how your data is shared and manage your communication preferences.",
        twitter_title: "Privacy Settings | Control Your Data | TradeReply",
        twitter_description: "Control your privacy settings on TradeReply. Adjust how your data is shared and manage your communication preferences.",
    };

    return (
        <>
            <AccountLayout>
                <MetaHead props={metaArray} />
                <div className="privacy_sec">
                    <SidebarHeading title="Privacy" />
                    <Row>
                        <PersonalRecommendations />
                        <CommunicationPreferences />
                        <DeleteMyAccount />
                    </Row>
                    <div>
                        <p className="fw-600">
                            You can manage your cookie preferences — including targeted advertising — at any time by clicking the cookie icon in the bottom-right corner of the screen.
                            If you’re unable to access the cookie settings or wish to submit a data or ad-related request, you may do so through our <Link href="/help" target="_blank">Support Center</Link>. This option is available to all users, including those in regions with specific privacy laws such as the European Union, California, and other U.S. states.
                        </p>
                    </div>
                </div>
            </AccountLayout>
        </>
    )
}
