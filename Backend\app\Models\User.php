<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use App\Mail\AccountLockedMail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Laravel\Cashier\Billable;
use Laravel\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens;
    use Notifiable;
    use Billable;
    use Notifiable;
    //    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'first_name',
        'last_name',
        'email',
        'phone_number',
        'email_verified_at',
        'first_security_verification_at',
        'two_factor_enabled',
        'two_factor_secret',
        'two_factor_enabled_at',
        'two_factor_always_required',
        'last_two_factor_verification_at',
        'password',
        'role',
        'country',
        'language',
        'timezone',
        'currency',
        'number_format',
        'username',
        'username_change_count',
        'subscription_id',
        'login_attempts',
        'lockout_cycles',
        'lockout_until',
        'stripe_customer_id'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    public function setUsernameAttribute($value)
    {
        $this->attributes['username'] = strtolower($value);
    }

    public function activeSubscription()
    {
        return $this->hasOne(UserSubscription::class)
            ->where('status', 'active')
            ->where(function ($query) {
                $query->whereNull('ends_at')
                      ->orWhere('ends_at', '>', now());
            });
    }

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'first_security_verification_at' => 'datetime',
        'two_factor_enabled_at' => 'datetime',
        'last_two_factor_verification_at' => 'datetime',
        'password' => 'hashed',
    ];


    public function educationProgress()
    {
        return $this->belongsToMany(Education::class, 'article_progress', 'user_id', 'article_id')
            ->withPivot('progress')
            ->withTimestamps();
    }


    /**
     * Relationship: A user belongs to a subscription.
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(Subscription::class);
    }

    public function lockAccountEmail()
    {
        try {
            Mail::to($this->email)->send(new AccountLockedMail($this));
        } catch (\Exception $e) {
            Log::error('Failed to send account lock email: ' . $e->getMessage(), ['email' => $this->email]);
        }
    }

    public function secretQuestions()
    {
        return $this->hasMany(UserSecretQuestion::class);
    }

    public function trades()
    {
        return $this->hasMany(Trade::class);
    }

    public function billingDetail()
    {
        return $this->hasOne(BillingDetail::class,'user_id');
    }

    public function addresses()
    {
        return $this->hasMany(Address::class);
    }

    public function loginActivities()
    {
        return $this->hasMany(LoginActivity::class);
    }

    public function defaultAddress()
    {
        return $this->hasOne(Address::class)->where('is_default', true);
    }

    /**
     * Check if 2FA verification is required for this user
     *
     * @param bool $hasValidRemember2FA Whether user has a valid remember 2FA cookie
     */
    public function requiresTwoFactorVerification(bool $hasValidRemember2FA = false): bool
    {
        // If 2FA is not enabled, no verification required
        if (!$this->two_factor_enabled) {
            return false;
        }

        // If "always require" is enabled, always require verification (ignore remember 2FA cookie)
        if ($this->two_factor_always_required) {
            return true;
        }

        // If user has a valid remember 2FA cookie, bypass 2FA verification
        if ($hasValidRemember2FA) {
            return false;
        }

        // Always require 2FA verification when enabled (removed last verification time check)
        return true;
    }

    /**
     * Record successful 2FA verification
     */
    public function recordTwoFactorVerification(): void
    {
        $this->update([
            'last_two_factor_verification_at' => now()
        ]);
    }
}
