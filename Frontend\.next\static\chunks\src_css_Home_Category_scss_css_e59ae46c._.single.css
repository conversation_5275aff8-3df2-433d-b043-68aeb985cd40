/* [project]/src/css/Home/Category.scss.css [app-client] (css) */
:root {
  --font-g<PERSON>roy: "<PERSON><PERSON>", sans-serif;
}

.categorySec .container {
  max-width: 1080px;
}

.categorySec_heading h1 {
  font-weight: 800;
  font-size: 3rem !important;
}

.categorySec_heading p {
  letter-spacing: -.1px;
  margin: 30px 0;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 28px;
}

@media (max-width: 991px) {
  .categorySec_heading p {
    margin: 20px 0;
    font-size: 1rem;
    line-height: 22px;
  }
}

.categorySec_search .commonSearch {
  max-width: 400px;
  margin: 0 auto;
}

.categorySec_search .commonSearch .form-control {
  width: 100%;
}

.categorySec_fliters {
  padding: 30px 0 50px;
}

@media (max-width: 991px) {
  .categorySec_fliters {
    padding: 20px 0 30px;
  }
}

@media (max-width: 767px) {
  .categorySec_fliters {
    padding: 20px 0 10px;
  }
}

.categorySec_fliters_inner {
  align-items: center;
  margin-bottom: 30px;
  padding: 0 1.5rem;
  display: flex;
}

@media (max-width: 991px) {
  .categorySec_fliters_inner {
    padding: 0 1rem;
  }
}

@media (max-width: 767px) {
  .categorySec_fliters_inner {
    margin-bottom: 20px;
  }
}

.categorySec_fliters_inner .slider {
  scrollbar-width: none;
  display: flex;
  overflow-x: auto;
}

.categorySec_fliters_inner .slider::-webkit-scrollbar {
  display: none;
}

.categorySec_fliters_inner .scroll-btn {
  color: #fff;
  cursor: pointer;
  background-color: #00adef;
  border: none;
  border-radius: 10rem;
  justify-content: center;
  align-items: center;
  min-width: 30px;
  min-height: 30px;
  padding: 0;
  font-size: 1.2rem;
  display: flex;
  position: relative;
}

.categorySec_fliters_inner .scroll-btn.left {
  left: -30px;
}

@media (max-width: 767px) {
  .categorySec_fliters_inner .scroll-btn.left {
    left: -14px;
  }
}

.categorySec_fliters_inner .scroll-btn.left img {
  transform: rotate(180deg);
}

.categorySec_fliters_inner .scroll-btn.right {
  right: -30px;
}

@media (max-width: 767px) {
  .categorySec_fliters_inner .scroll-btn.right {
    right: -14px;
  }
}

.categorySec_fliters_inner .scroll-btn:hover {
  background-color: #00adef;
}

.categorySec_fliters_inner .scroll-btn.disabled, .categorySec_fliters_inner .scroll-btn:disabled {
  background-color: #414c60;
}

.categorySec_fliters_boxbutton {
  letter-spacing: -.1px;
  cursor: pointer;
  background-color: rgba(0, 173, 239, .15);
  border: 0;
  border-radius: 5px;
  justify-content: center;
  align-items: center;
  width: auto;
  min-height: 35px;
  margin-right: 10px;
  padding: 5px 10px;
  font-size: 1rem;
  font-weight: 600;
  line-height: 1.5rem;
  transition: all .3s ease-in-out;
  display: flex;
  color: #fff !important;
}

@media (max-width: 767px) {
  .categorySec_fliters_boxbutton {
    font-size: .875rem;
  }
}

.categorySec_fliters_boxbutton .active, .categorySec_fliters_boxbutton a {
  color: #fff !important;
}

.categorySec_fliters_boxbutton:last-child {
  margin-right: 0;
}

.categorySec_fliters_boxbutton:hover, .categorySec_fliters_boxbutton.active, .categorySec_fliters_boxbutton .selected {
  background-color: #00adef;
}

.categorySec_fliters_boxadd {
  background-color: #00adef;
  border-radius: 15px;
  align-items: center;
  padding: 5px 15px;
  display: inline-flex;
}

.categorySec_fliters_boxadd h6 {
  color: #fff;
}

.categorySec_pagination {
  justify-content: flex-end;
  display: flex;
}

.categorySec_term_content p {
  color: #c5c5c5;
  letter-spacing: -.1px;
  font-size: 1.125rem;
  font-weight: 500;
  line-height: 28px;
}

/*# sourceMappingURL=src_css_Home_Category_scss_css_e59ae46c._.single.css.map*/