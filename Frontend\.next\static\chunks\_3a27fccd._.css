/* [project]/src/css/common/CommonButton.scss.css [app-client] (css) */
:root {
  --font-gilroy: "<PERSON><PERSON>", sans-serif;
}

.btn-style, .btn-primary {
  text-align: center;
  text-transform: capitalize;
  color: #fff;
  background-color: #00adef;
  border: 0;
  border-radius: 10rem;
  justify-content: center;
  align-items: center;
  min-width: 150px;
  min-height: 66px;
  padding: .5rem 1.5rem;
  font-size: 1.25rem;
  font-weight: 600;
  transition: all .3s ease-in-out;
  display: inline-flex;
}

.btn-style span, .btn-primary span {
  line-height: 1;
}

@media (max-width: 1599px) {
  .btn-style, .btn-primary {
    min-height: 66px;
  }
}

@media (max-width: 1199px) {
  .btn-style, .btn-primary {
    min-height: 56px;
    font-size: 1.125rem;
    font-weight: 500;
  }
}

@media (max-width: 767px) {
  .btn-style, .btn-primary {
    min-height: 46px;
    font-size: 1rem;
  }
}

.btn-style:hover, .btn-primary:hover {
  color: #fff;
  background-color: #0099d1;
}

.btn-style.transparent, .btn-primary.transparent {
  background-color: rgba(0, 0, 0, 0);
  border: none;
}

.btn-style.white-btn, .btn-primary.white-btn {
  color: #000;
  background: #fff;
}

.btn-style.white-btn:hover, .btn-primary.white-btn:hover {
  color: #fff;
  background: #00adef;
}

.btn-style.yellow-btn, .btn-primary.yellow-btn {
  color: #fff;
  background-color: #fea500;
}

.btn-style.yellow-btn:hover, .btn-primary.yellow-btn:hover {
  color: #fff;
  background-color: #c9870d;
}

.btn-style.gray-btn, .btn-primary.gray-btn {
  color: #fff;
  background-color: #5e6165 !important;
}

.btn-style.gray-btn:hover, .btn-primary.gray-btn:hover {
  color: #fff;
  background-color: #708090;
}

.btn-style.gradient-btn, .btn-primary.gradient-btn {
  color: #fff;
  background: linear-gradient(75deg, #00aeef, #1f5aff 50.31%, #da00ff);
}

.btn-style.gradient-btn:hover, .btn-primary.gradient-btn:hover {
  color: #fff;
  background: linear-gradient(75deg, #0043ff, #1f5aff 50.31%, #da00ff);
}

.btn-style.green-btn, .btn-primary.green-btn {
  color: #fff;
  background-color: #32cd33;
}

.btn-style.green-btn:hover, .btn-primary.green-btn:hover {
  color: #fff;
  background-color: #2bb72b;
}

.btn-style.red-btn, .btn-primary.red-btn {
  color: #fff;
  background-color: #ff696a;
}

.btn-style.red-btn:hover, .btn-primary.red-btn:hover {
  color: #fff;
  background-color: #e65f60;
}

.btn-style.border-btn, .btn-primary.border-btn {
  color: #fff;
  background: none;
  border: 1px solid #00adef;
}

.btn-style.border-btn:hover, .btn-primary.border-btn:hover {
  color: #fff;
  background: #00adef;
}

.btn-style .onlyIcon, .btn-primary .onlyIcon {
  margin-right: 15px;
  display: inline-flex;
}

.btn-style:disabled, .btn-style.disabled, .btn-primary:disabled, .btn-primary.disabled {
  color: #fff;
  cursor: not-allowed;
  opacity: 1;
  background: #c5c5d5;
}

:disabled, .disabled {
  color: #fff;
  cursor: not-allowed;
  opacity: 1;
  background-color: #414c60;
}

.white20 {
  background-color: rgba(255, 255, 255, .12);
  width: 100%;
}

/* [project]/src/css/common/Header.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

header {
  z-index: 9998;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  left: 0;
}

.home-page .siteHeader {
  border-bottom: 0;
  background-color: #000 !important;
}

@media (min-width: 1200px) {
  .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show, .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle.show:after, .home-page .siteHeader .navMenu .common_dropdown.dropdown .dropdown-toggle:hover:after {
    transform: rotate(180deg);
  }
}

.home-page .siteHeader .navMenu .common_dropdown.dropdown.show .dropdown-toggle:after {
  transform: rotate(180deg);
}

@media (min-width: 1200px) {
  .home-page .siteHeader .navMenu .common_dropdown .dropdown-menu {
    background-color: #1e222d !important;
  }
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.active, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus {
  background-color: #2a2e39 !important;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon {
  color: #00b9ff;
  font-weight: 700;
  transition: none;
  background: linear-gradient(to right, #2a2e39, #1e222d) !important;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path {
  fill: #00b9ff;
}

.home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .home-page .siteHeader .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
  background: #2a2e39 !important;
}

@media (max-width: 1199px) {
  .home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon {
    color: #00aeef;
    font-weight: 700;
    transition: none;
    background: linear-gradient(to right, #000, #2d2d2d) !important;
  }

  .home-page .siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path {
    fill: #00b9ff;
  }

  .home-page .siteHeader .navbar-collapse {
    background-color: rgba(0, 0, 0, .9) !important;
  }
}

@media (min-width: 1200px) {
  .home-page .siteHeader .navbar-collapse .nav-link:hover, .home-page .siteHeader .navbar-collapse .nav-link.active, .home-page .siteHeader .navbar-collapse .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .languageDropdown {
    width: 64px;
  }
}

@media (min-width: 1200px) and (max-width: 1199px) {
  .home-page .languageDropdown, .home-page .languageDropdown .common_dropdown {
    width: 100%;
  }
}

@media (min-width: 1200px) {
  .home-page .languageDropdown .common_dropdown .nav-link:hover, .home-page .languageDropdown .common_dropdown .nav-link.active, .home-page .languageDropdown .common_dropdown .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }

  .home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .home-page .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
    background: #2a2e39 !important;
  }
}

.siteHeader {
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  background-color: #031940;
  border-bottom: 1px solid #064197;
  justify-content: center;
  align-items: center;
  height: 80px;
  padding: 1rem 0;
  display: flex;
}

.siteHeader .btn-style {
  min-width: 169px;
  min-height: 56px;
}

@media (max-width: 1199px) {
  .siteHeader .btn-style {
    min-width: 120px;
    min-height: 40px;
    padding: 8px 1rem;
    font-size: 14px;
  }
}

@media (max-width: 575px) {
  .siteHeader .btn-style {
    min-width: 80px;
    min-height: 34px;
    font-size: 14px;
  }
}

@media (max-width: 1199px) {
  .siteHeader {
    z-index: 9999;
    -webkit-backdrop-filter: none;
    backdrop-filter: none;
  }
}

@media (max-width: 767px) {
  .siteHeader {
    padding: .625rem 0;
  }
}

.siteHeader .navbar {
  width: 100%;
  padding: 0;
}

.siteHeader .navbar .brandLogo img {
  width: 100%;
  max-width: 190px;
}

@media (max-width: 767px) {
  .siteHeader .navbar .brandLogo img {
    max-width: 150px;
    margin-right: 0;
  }
}

@media (max-width: 360px) {
  .siteHeader .navbar .brandLogo img {
    max-width: 120px;
    margin-right: 0;
  }
}

.siteHeader .navbar-collapse {
  height: auto !important;
}

.siteHeader .navbar-collapse .nav-link {
  white-space: nowrap;
  color: #fff;
  background-color: rgba(0, 0, 0, 0);
  align-items: center;
  padding: .5rem 1.5rem;
  font-size: 1.25rem;
  font-weight: 400;
  display: flex;
}

.siteHeader .navbar-collapse .nav-link:hover, .siteHeader .navbar-collapse .nav-link.active, .siteHeader .navbar-collapse .nav-link:focus {
  color: #00adef;
}

@media (min-width: 1200px) {
  .siteHeader .navbar-collapse .nav-link:hover, .siteHeader .navbar-collapse .nav-link.active, .siteHeader .navbar-collapse .nav-link:focus {
    color: #fff;
    background-color: #283f67 !important;
  }

  .siteHeader .navbar-collapse .nav-link {
    margin: 0 3px;
  }
}

@media (max-width: 1199px) {
  .siteHeader .navbar-collapse .nav-link {
    border-bottom: 1px solid rgba(255, 255, 255, .2);
    padding: 1.25rem 0;
    font-size: 1.125rem;
  }

  .siteHeader .navbar-collapse .nav-link img {
    width: 22px;
  }

  .siteHeader .navbar-collapse .nav-link.white_stroke_icon {
    color: #00aeef;
    background: linear-gradient(to right, #031940, #283f67);
    font-weight: 700;
    transition: none;
  }

  .siteHeader .navbar-collapse .nav-link.white_stroke_icon svg path {
    fill: #00b9ff;
  }

  .siteHeader .navbar-collapse {
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    z-index: 9999;
    background-color: rgba(3, 25, 64, .9);
    width: 350px;
    padding: 0;
    transition: all .2s ease-in-out;
    display: block;
    position: fixed;
    top: 0;
    left: -350px;
    height: 100vh !important;
  }

  .siteHeader .navbar-collapse a {
    text-align: left;
    justify-content: flex-start;
    display: flex;
  }

  .siteHeader .navbar-collapse.show {
    height: 100vh;
    left: 0;
  }

  .siteHeader .navbar-collapse .navMenu {
    max-height: calc(100vh - 84px);
    max-height: calc(100dvh - 84px);
    padding: 20px;
    overflow-y: auto;
  }
}

@media (max-width: 767px) {
  .siteHeader .navbar-collapse {
    width: 100%;
    left: -100%;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle {
  border-radius: 0;
  padding: .5rem 1.5rem !important;
}

@media (max-width: 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle {
    border-bottom: 1px solid rgba(255, 255, 255, .2);
    width: 100%;
    padding: 1.25rem 0 !important;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:after {
  background-image: url("https://cdn.tradereply.com/dev/site-assets/icons/tradereply-drop-arrow.svg");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 1.15rem;
  border: 0;
  width: 1.15rem;
  height: 1.15rem;
  margin-left: 1rem;
  transition: all .3s ease-in-out;
  display: block;
}

@media (max-width: 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:after {
    margin-left: 0;
    position: absolute;
    right: 0;
  }
}

@media (min-width: 1200px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show, .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover {
    color: #fff;
    background-color: #283f67;
  }

  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle.show:after, .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-toggle:hover:after {
    transform: rotate(180deg);
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown.show .dropdown-toggle:after {
  transform: rotate(180deg);
}

@media screen and (max-width: 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu {
    background-color: rgba(0, 0, 0, 0);
    border: 0;
    padding: 0;
    position: static;
  }
}

.siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link {
  align-items: start;
  padding: .875rem 1.5rem;
  font-weight: 400 !important;
}

@media screen and (max-width: 1199px) {
  .siteHeader .navbar-collapse .navMenu .common_dropdown.dropdown .dropdown-menu .nav-link {
    padding: .875rem 1rem;
  }
}

.siteHeader .navbar .navbar-toggler {
  background-color: rgba(0, 0, 0, 0);
  width: 24px;
  height: 18px;
  margin-left: 0;
  padding: 0;
  position: relative;
}

.siteHeader .navbar .navbar-toggler:focus {
  box-shadow: none;
}

@media (max-width: 1199px) {
  .siteHeader .navbar .navbar-toggler {
    margin-right: 13px;
  }
}

@media (max-width: 767px) {
  .siteHeader .navbar .navbar-toggler {
    margin-right: 13px;
  }
}

.siteHeader .navbar .navbar-toggler:after {
  content: "";
  background-color: #fff;
  width: 24px;
  height: 2px;
  transition: all .3s ease-in-out;
  position: absolute;
  bottom: 0;
  left: 0;
}

.siteHeader .navbar .navbar-toggler:before {
  content: "";
  background-color: #fff;
  width: 24px;
  height: 2px;
  transition: all .3s ease-in-out;
  position: absolute;
  top: 0;
  left: 0;
}

.siteHeader .navbar .navbar-toggler .navbar-toggler-icon {
  background-color: #fff;
  background-image: none;
  width: 24px;
  height: 2px;
  transition: all .3s ease-in-out;
  display: flex;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  border-radius: .625rem;
  align-items: center;
  padding: 0;
  font-size: 1.25rem;
  display: flex;
  padding: .5rem .2rem !important;
}

@media (max-width: 991px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1.125rem;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle:after {
  display: none;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-toggle.show svg path {
  fill: #00adef;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu {
  border: 1px solid rgba(255, 255, 255, .3);
  border-radius: .625rem;
  min-width: 200px;
  position: absolute;
  top: 45px;
}

@media screen and (max-width: 1199px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu {
    min-width: 100%;
    padding: 0;
    position: static;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item {
  color: #fff;
  padding: .625rem 1rem;
  font-size: 1.125rem;
  font-weight: 600;
}

@media (max-width: 991px) {
  .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item {
    font-size: 1rem;
  }
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item svg, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item img {
  margin-right: 10px;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:hover, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.active, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item:focus {
  background: #283f67;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon {
  color: #00b9ff;
  background: linear-gradient(to right, #283f67, #031940);
  font-weight: 700;
  transition: none;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon svg path {
  fill: #00b9ff;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon:hover, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu .dropdown-item.white_stroke_icon.active {
  background: #283f67 !important;
}

.siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show svg, .siteHeader .navbar .common_dropdown.dropdown .dropdown-menu.show img {
  width: 18px;
}

@media screen and (max-width: 1199px) {
  .siteHeader .navbar .openmenuSidebar {
    border-bottom: 1px solid rgba(255, 255, 255, .5);
    padding: 30px 15px;
  }

  .siteHeader .navbar .openmenuSidebar .brandLogo {
    padding: 0;
  }

  .siteHeader .navbar .openmenuSidebar .brandLogo img {
    max-width: 150px;
  }

  .siteHeader .navbar .openmenuSidebar .navbar-toggler {
    position: absolute;
    right: 15px;
  }
}

.siteHeader.openmenu .navbar .navbar-toggler:after {
  background-color: #fff;
  transform: rotate(45deg)translate(-5px, -5px);
}

.siteHeader.openmenu .navbar .navbar-toggler:before {
  background-color: #fff;
  transform: rotate(-45deg)translate(-5px, 5px);
}

.siteHeader.openmenu .navbar .navbar-toggler .navbar-toggler-icon {
  opacity: 0;
}

.siteHeader .user_icon img {
  width: 26px;
  height: 26px;
}

@media screen and (max-width: 767px) {
  .siteHeader .sidebar_backdrop {
    display: none;
  }
}

.languageDropdown {
  width: 64px;
}

@media (max-width: 1199px) {
  .languageDropdown, .languageDropdown .common_dropdown {
    width: 100%;
  }
}

.languageDropdown .common_dropdown .nav-link:hover, .languageDropdown .common_dropdown .nav-link.active, .languageDropdown .common_dropdown .nav-link:focus {
  color: #fff;
  background-color: #283f67 !important;
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle {
  color: #fff;
  border: 0;
  align-items: center;
  padding: 0;
  font-size: 1.25rem;
  display: flex;
  border-radius: 0 !important;
}

@media (max-width: 991px) {
  .languageDropdown .common_dropdown.dropdown .dropdown-toggle {
    font-size: 1rem;
  }
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle svg {
  margin-right: 10px;
}

.languageDropdown .common_dropdown.dropdown .dropdown-toggle:focus, .languageDropdown .common_dropdown.dropdown .dropdown-toggle:hover {
  background-color: rgba(0, 0, 0, 0) !important;
}

@media (max-width: 1199px) {
  .languageDropdown .common_dropdown.dropdown .dropdown-toggle {
    width: 100%;
  }
}

.languageDropdown .globalIcon .icon {
  transition: opacity .3s;
}

.languageDropdown .globalIcon .blue, .languageDropdown .nav-item:hover .globalIcon .black, .languageDropdown .nav-item.show .globalIcon .black {
  display: none;
}

.languageDropdown .nav-item:hover .globalIcon .blue, .languageDropdown .nav-item.show .globalIcon .blue {
  display: block;
}

.userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name {
  display: none;
}

@media screen and (max-width: 1199px) {
  .userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name {
    padding-left: 10px;
    font-size: 18px;
    display: block;
  }

  .userDropdown.common_dropdown.dropdown .dropdown-toggle .user_name svg {
    width: 26px;
    height: 26px;
  }

  .userDropdown.common_dropdown.dropdown .dropdown-toggle {
    border-bottom: 0 !important;
  }
}

.userDropdown.common_dropdown.dropdown .dropdown-toggle:hover {
  background-color: rgba(0, 0, 0, 0) !important;
}

@media (max-width: 1199px) {
  .brandLogo {
    display: flex;
  }

  .brandLogo img {
    max-width: 150px;
  }
}

@media (max-width: 767px) {
  .brandLogo img {
    max-width: 110px;
  }
}

@media (max-width: 359px) {
  .brandLogo img {
    max-width: 100px;
  }
}

.sidebar_backdrop {
  z-index: 1000;
  background-color: rgba(0, 0, 0, .2);
  width: 100%;
  height: 100vh;
  transition: all .2s ease-in-out;
  position: fixed;
  top: 0;
  left: 0;
}

.image_color_to_white {
  filter: brightness(0) invert();
}

@media (min-width: 1200px) {
  .nav-link:hover, .nav-link.active, .nav-link:focus {
    color: #fff;
    background-color: #2a2e39 !important;
  }
}

/* [project]/src/css/common/Footer.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.site_footer {
  background-color: #000;
}

.site_footer_inner {
  padding: 70px 0;
}

@media screen and (max-width: 991px) {
  .site_footer_inner {
    padding: 40px 0;
  }
}

.site_footer_logo img {
  width: 200px;
}

.site_footer_content p {
  color: rgba(255, 255, 255, .65);
  letter-spacing: -.1px;
  margin-top: 20px;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
}

@media screen and (max-width: 991px) {
  .site_footer_content p {
    font-size: 16px;
  }
}

@media screen and (max-width: 767px) {
  .site_footer_links {
    margin-top: 20px;
  }
}

.site_footer_links h4 {
  color: #c5c5d5;
  margin-bottom: 1.25rem;
  font-size: 1.65rem;
  font-weight: 600;
  line-height: 35px;
}

@media screen and (max-width: 991px) {
  .site_footer_links h4 {
    font-size: 18px;
  }
}

.site_footer_links ul li a {
  letter-spacing: -.1px;
  color: #fff;
  padding-bottom: 10px;
  font-size: 20px;
  font-weight: 600;
  line-height: 24.5px;
  transition: all .3s ease-in-out;
}

@media screen and (max-width: 991px) {
  .site_footer_links ul li a {
    font-size: 16px;
  }
}

.site_footer_links ul li a:hover, .site_footer_links ul li a.active {
  color: #00adef;
}

.site_footer_copyright {
  border-top: 1px solid #fff;
  padding: 1.25rem 0;
}

.site_footer_copyright p {
  text-align: center;
  letter-spacing: -.1px;
  font-size: 18px;
  font-weight: 600;
  line-height: 26px;
}

@media screen and (max-width: 991px) {
  .site_footer_copyright p {
    font-size: 16px;
  }
}

/* [project]/src/css/account/AccountSidebar.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.Account_sidebar {
  z-index: 999;
  background-color: #031940;
  width: 355px;
  height: calc(100vh - 88px);
  padding: 1.875rem 1.5rem 1rem;
  transition: all .3s ease-in-out;
  position: -webkit-sticky;
  position: sticky;
  top: 88px;
  left: 0;
  overflow-y: auto;
}

@media (max-width: 1199px) {
  .Account_sidebar {
    z-index: 9999;
    -webkit-backdrop-filter: blur(5px);
    backdrop-filter: blur(5px);
    background-color: rgba(3, 25, 64, .9);
    width: 325px;
    height: 100vh;
    padding: 6rem 1.5rem 1.875rem;
    position: fixed;
    top: 0;
    left: -325px;
  }
}

@media (max-width: 767px) {
  .Account_sidebar {
    width: 100%;
    left: -100%;
  }
}

@media (max-width: 1199px) {
  .Account_sidebar.opensidebar {
    left: 0;
  }

  .Account_sidebar_head {
    text-align: center;
    width: 100%;
    position: absolute;
    top: 22px;
    left: 50%;
    transform: translateX(-50%);
  }

  .Account_sidebar_head:after {
    content: "";
    background-color: #666;
    width: 100%;
    height: 1px;
    position: absolute;
    bottom: -20px;
    right: 0;
  }

  .Account_sidebar_head .headLogo img {
    width: 150px;
  }
}

.Account_sidebar ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.Account_sidebar ul li {
  margin-bottom: 10px;
}

.Account_sidebar ul li:last-child {
  margin-bottom: 0;
}

.Account_sidebar ul li a {
  color: #fff;
  letter-spacing: -.1px;
  text-align: left;
  z-index: 1;
  border-left: 3px solid rgba(0, 0, 0, 0);
  align-items: center;
  padding: 15px 1rem;
  font-size: 1.125rem;
  font-weight: 600;
  line-height: 24.5px;
  display: flex;
  position: relative;
}

.Account_sidebar ul li a:after {
  content: "";
  z-index: -1;
  background: linear-gradient(90deg, rgba(4, 73, 140, .5) 0%, rgba(4, 73, 140, 0) 100%);
  width: 0;
  height: 100%;
  transition: all .3s ease-in-out;
  position: absolute;
  bottom: 0;
  left: 0;
}

.Account_sidebar ul li a span {
  margin-right: 15px;
}

.Account_sidebar ul li a span svg {
  width: 20px;
}

.Account_sidebar ul li a:hover, .Account_sidebar ul li a.active {
  border-color: #00adef;
}

.Account_sidebar ul li a:hover:after, .Account_sidebar ul li a.active:after {
  width: 100%;
}

.Account_sidebar_bottom_link {
  border-top: 1px solid #04498c;
  margin-top: 1.25rem;
  padding-top: 1.25rem;
}

.Account_sidebar_bottom_link ul li a svg {
  margin-left: 1rem;
  transition: all .3s ease-in-out;
}

.Account_sidebar_bottom_link ul li a:hover svg {
  margin-left: 1.5rem;
}

.Account_sidebar .filter_toggle {
  display: none;
}

@media (max-width: 1199px) {
  .Account_sidebar .filter_toggle {
    display: block;
    position: absolute;
    top: 0;
    left: auto;
    right: 20px;
  }
}

.Account_sidebar::-webkit-scrollbar {
  border-radius: 1rem;
  width: 5px;
  height: 4px;
}

.Account_sidebar::-webkit-scrollbar-thumb {
  background-color: #0557a3;
  border-radius: 1rem;
}

.filter_toggle {
  display: none;
}

@media (max-width: 1199px) {
  .filter_toggle {
    display: block;
    position: absolute;
    top: 51px;
    left: 20px;
  }
}

.filter_toggle_btn {
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  width: 26px;
  height: 22px;
  margin-left: 1.25rem;
  padding: 0;
  position: relative;
}

.filter_toggle_btn:focus {
  box-shadow: none;
  border: 0;
  outline: 0;
}

@media (max-width: 1199px) {
  .filter_toggle_btn {
    margin-left: 0;
  }
}

@media (max-width: 767px) {
  .filter_toggle_btn {
    margin-left: 0;
  }
}

.filter_toggle_btn:after {
  content: "";
  background-color: #fff;
  width: 26px;
  height: 2px;
  transition: all .3s ease-in-out;
  position: absolute;
  bottom: 0;
  left: 0;
}

.filter_toggle_btn:before {
  content: "";
  background-color: #fff;
  width: 26px;
  height: 2px;
  transition: all .3s ease-in-out;
  position: absolute;
  top: 0;
  left: 0;
}

.filter_toggle_btn span {
  background-color: #00adef;
  background-image: none;
  width: 20px;
  height: 2px;
  transition: all .3s ease-in-out;
  display: flex;
}

.filter_toggle_btn.active:after {
  transform: rotate(45deg)translate(-6px, -6px);
}

.filter_toggle_btn.active:before {
  transform: rotate(-45deg)translate(-8px, 8px);
}

.filter_toggle_btn.active span {
  opacity: 0;
}

/* [project]/src/css/account/AccountLayout.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.Account_layout_main {
  display: flex;
}

.Account_layout_rightaside {
  color: #fff;
  width: calc(100% - 355px);
  padding: 2.813rem 1.875rem;
  position: relative;
}

@media (min-width: 992px) {
  .Account_layout_rightaside .mb-lg-4 {
    margin-bottom: 1.875rem !important;
  }
}

@media (max-width: 1199px) {
  .Account_layout_rightaside {
    width: 100%;
    padding: 2.813rem 1rem;
  }
}

.sidebar_heading {
  width: 100%;
  margin-bottom: 26px;
}

@media screen and (max-width: 1199px) {
  .sidebar_heading {
    padding-left: 45px;
  }
}

.sidebar_heading_top {
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  display: flex;
}

.sidebar_heading h2 {
  font-family: Gilroy-Bold, sans-serif;
  font-size: 48px;
  font-weight: 400;
  display: inline-flex;
}

@media (max-width: 1199px) {
  .sidebar_heading h2 {
    font-size: 1.688rem;
  }
}

@media (max-width: 767px) {
  .sidebar_heading h2 {
    font-size: 1.5rem;
  }
}

.sidebar_heading_icon button {
  color: #00adef;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  padding: 0;
  font-size: 1.15rem;
  font-weight: 600;
  transition: all .3s ease-in-out;
}

@media (max-width: 991px) {
  .sidebar_heading_icon button {
    justify-content: flex-end;
    align-items: center;
    font-size: 14px;
    display: flex;
  }
}

.sidebar_heading_icon button .me-2 {
  margin-right: 10px !important;
}

.sidebar_heading_icon button .ms-2 {
  margin-left: 10px !important;
}

.sidebar_heading_icon button svg {
  width: 16px;
  margin-left: 10px;
  transition: all .3s ease-in-out;
}

@media (max-width: 991px) {
  .sidebar_heading_icon button svg {
    width: 14px;
  }
}

@media (max-width: 767px) {
  .sidebar_heading_icon button svg {
    width: 10px;
    margin-left: 0;
  }
}

.sidebar_heading_icon button svg path {
  fill: #00adef;
}

.sidebar_heading_icon button:hover {
  color: #fea500;
}

.sidebar_heading_icon button:hover svg {
  margin-left: 12px;
}

@media (max-width: 991px) {
  .sidebar_heading_icon button:hover svg {
    margin-left: 10px;
  }
}

.sidebar_heading_icon button:hover svg path {
  fill: #fea500;
}

.common_blackcard, .common_whitecard {
  background-color: #191c23;
  border: 1px solid #04498c;
  border-radius: 15px;
  width: 100%;
  padding: 10px;
}

.common_blackcard_innerheader, .common_whitecard_innerheader {
  background-color: #031940;
  border-radius: 15px;
  justify-content: space-between;
  align-items: center;
  padding: .625rem 1.25rem;
  font-family: Gilroy-Semibold, sans-serif;
  font-weight: 400;
  display: flex;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader, .common_whitecard_innerheader {
    padding: .625rem;
  }
}

.common_blackcard_innerheader p, .common_whitecard_innerheader p {
  color: #fff;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader p, .common_whitecard_innerheader p {
    font-size: 14px;
  }
}

@media (max-width: 767px) {
  .common_blackcard_innerheader p, .common_whitecard_innerheader p {
    font-size: 14px;
  }
}

.common_blackcard_innerheader_content, .common_whitecard_innerheader_content {
  padding-right: 30px;
  font-size: 18px;
  font-weight: 400;
}

.common_blackcard_innerheader_content h6, .common_whitecard_innerheader_content h6 {
  font-weight: 600;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_content, .common_whitecard_innerheader_content {
    padding-right: 15px;
  }

  .common_blackcard_innerheader_content h6, .common_whitecard_innerheader_content h6 {
    font-size: 18px !important;
  }
}

@media (max-width: 767px) {
  .common_blackcard_innerheader_content, .common_whitecard_innerheader_content {
    padding-right: 5px;
  }
}

@media (max-width: 400px) {
  .common_blackcard_innerheader_content h6, .common_whitecard_innerheader_content h6 {
    font-size: 14px !important;
  }
}

.common_blackcard_innerheader_icon button, .common_whitecard_innerheader_icon button {
  color: #00adef;
  white-space: nowrap;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  padding: 0;
  font-family: Gilroy-Semibold, sans-serif;
  font-size: 18px;
  font-weight: 600;
  transition: all .3s ease-in-out;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_icon button, .common_whitecard_innerheader_icon button {
    justify-content: flex-end;
    align-items: center;
    font-size: 14px;
    display: flex;
  }
}

.common_blackcard_innerheader_icon button .me-2, .common_whitecard_innerheader_icon button .me-2 {
  margin-right: 10px !important;
}

.common_blackcard_innerheader_icon button .ms-2, .common_whitecard_innerheader_icon button .ms-2 {
  margin-left: 10px !important;
}

.common_blackcard_innerheader_icon button .link-icon, .common_whitecard_innerheader_icon button .link-icon {
  flex-shrink: 0;
  height: auto;
}

.common_blackcard_innerheader_icon button svg, .common_whitecard_innerheader_icon button svg {
  width: 16px;
  transition: all .3s ease-in-out;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_icon button svg, .common_whitecard_innerheader_icon button svg {
    width: 14px;
  }
}

@media (max-width: 767px) {
  .common_blackcard_innerheader_icon button svg, .common_whitecard_innerheader_icon button svg {
    width: 10px;
    margin-left: 0;
  }
}

.common_blackcard_innerheader_icon button svg path, .common_whitecard_innerheader_icon button svg path {
  fill: #00adef;
}

.common_blackcard_innerheader_icon button:hover, .common_whitecard_innerheader_icon button:hover {
  color: #fea500;
}

.common_blackcard_innerheader_icon button:hover svg, .common_whitecard_innerheader_icon button:hover svg {
  margin-left: 12px;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_icon button:hover svg, .common_whitecard_innerheader_icon button:hover svg {
    margin-left: 10px;
  }
}

.common_blackcard_innerheader_icon button:hover svg path, .common_whitecard_innerheader_icon button:hover svg path {
  fill: #fea500;
}

.common_blackcard_innerheader_tradeacct, .common_whitecard_innerheader_tradeacct {
  width: 200px;
  min-width: 200px;
}

.common_blackcard_innerheader_tradeacct h6, .common_whitecard_innerheader_tradeacct h6 {
  color: #fff;
  background-color: #04498c;
  border-top-left-radius: 10px;
  border-top-right-radius: 10px;
  padding: 8px 15px;
  font-size: 1.25rem;
  line-height: normal;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_tradeacct h6, .common_whitecard_innerheader_tradeacct h6 {
    font-size: 16px;
  }
}

.common_blackcard_innerheader_tradeacct p, .common_whitecard_innerheader_tradeacct p {
  color: #000;
  background-color: #fff;
  border-bottom-right-radius: 10px;
  border-bottom-left-radius: 10px;
  margin: 0;
  padding: 8px 15px;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: normal;
}

@media (max-width: 991px) {
  .common_blackcard_innerheader_tradeacct p, .common_whitecard_innerheader_tradeacct p {
    font-size: 16px;
  }
}

.common_blackcard_innerbody, .common_whitecard_innerbody {
  padding: 1.25rem .625rem;
}

.connetionTable .common_blackcard_innerbody, .connetionTable .common_whitecard_innerbody, .removePadding .common_blackcard_innerbody, .removePadding .common_whitecard_innerbody {
  padding: 0;
}

@media (max-width: 991px) {
  .account_card.pullcontent .common_blackcard_innerheader_icon, .account_card.pullcontent .common_whitecard_innerheader_icon {
    margin-top: 10px;
  }
}

.common_whitecard {
  background-color: #fff !important;
}

.common_whitecard_innerbody {
  padding: .8rem .3rem 0;
}

.account_card .label {
  color: #fff;
  font-size: 1.125rem;
  font-weight: 600;
}

.account_card_list_btns {
  justify-content: end;
  align-items: center;
  gap: 10px;
  display: flex;
}

.account_card_list_btns .btn-style {
  min-height: 40px;
  font-size: 16px !important;
}

.account_card_list_form {
  width: 40%;
}

.account_card_list_form .row {
  width: 100% !important;
}

.account_card_list ul li {
  color: #fff;
  justify-content: space-between;
  margin-bottom: 1.25rem;
  font-size: 1.125rem;
  font-weight: 600;
  display: flex;
}

@media (max-width: 991px) {
  .account_card_list ul li {
    margin-bottom: 1rem;
    font-size: 15px;
  }
}

.account_card_list ul li:last-child {
  margin-bottom: 0;
}

.account_card_list ul li span {
  color: #fff;
}

.account_card_list p {
  color: #c5c5c5;
}

.account_card_redeem .form-control {
  width: calc(100% - 136px);
  margin-right: 10px;
}

.account_card_redeem ::placeholder {
  color: #fff;
  opacity: 1;
}

.account_card_redeem .btn-style {
  min-width: 150px;
  min-height: 56px;
  padding: 8px 15px;
  font-size: 16px;
  font-weight: 600;
  line-height: 26px;
}

@media (max-width: 991px) {
  .account_card_redeem .btn-style {
    min-width: 130px;
    min-height: 52px;
    padding: 8px 15px;
    font-size: 14px;
    line-height: 24px;
  }
}

.account_card_redeem .error-messages .success {
  color: #32cd33;
}

.account_card_redeem .error-messages .invalid {
  color: #ff696a;
}

.account_card_checkup_verify {
  width: calc(100% - 150px);
  padding-right: 30px;
}

@media (max-width: 991px) {
  .account_card_checkup_verify {
    width: calc(100% - 100px);
    padding-right: 0;
  }
}

.account_card_checkup_chart {
  text-align: center;
  width: 150px;
}

@media (max-width: 991px) {
  .account_card_checkup_chart {
    width: 100px;
  }

  .account_card_checkup_chart .CircularProgressbar_text h6 {
    font-size: 14px;
  }
}

.account_card_subscription_list {
  flex-wrap: wrap;
  display: flex;
}

.account_card_subscription_list li {
  width: 25%;
}

@media (max-width: 767px) {
  .account_card_subscription_list li {
    width: 50%;
    padding: 5px 0;
  }

  .account_card_subscription_list li:nth-child(2n) {
    text-align: right;
  }
}

.account_card_subscription_list li p {
  color: #fff;
  margin-top: .5rem;
  font-weight: 600;
}

@media (max-width: 991px) {
  .account_card_subscription_list li p {
    font-size: 14px;
    line-height: 20px;
  }
}

@media (max-width: 767px) {
  .account_card_subscription_list li p {
    margin-top: 0;
  }
}

@media (max-width: 991px) {
  .account_card_subscription_list li h6 {
    font-size: 15px;
  }
}

.account_card_table .simple_table_imgIcon {
  min-width: 20px;
}

.account_card_table .tableless {
  border: 0;
}

.account_card_table .tableless:before {
  display: none;
}

.account_card_table .tableless .common_table tr th, .account_card_table .tableless .common_table tr td {
  border: 0;
}

.account_card_table .tableless .common_table tr td {
  color: #fff;
  border-bottom: 1px solid #666;
  font-weight: 600 !important;
}

.account_card_table .tableless .common_table thead tr {
  border-radius: 15px;
}

.account_card_table .tableless .common_table thead tr th {
  background-color: #031940;
  padding: 10px 15px;
  font-size: 1rem;
}

.account_card_table .tableless .common_table thead tr th:first-child {
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
}

.account_card_table .tableless .common_table thead tr th:last-child {
  border-top-right-radius: 15px;
  border-bottom-right-radius: 15px;
}

.account_card .add_phone_number, .account_card .add_number, .account_card .blue_text_btn {
  color: #00adef;
  letter-spacing: -.1px;
  background-color: rgba(0, 0, 0, 0);
  border: 0;
  padding: 0;
  font-size: 1.25rem;
  font-weight: 600;
  line-height: 24.5px;
  transition: all .3s ease-in-out;
}

@media (max-width: 991px) {
  .account_card .add_phone_number, .account_card .add_number, .account_card .blue_text_btn {
    font-size: 1rem;
  }
}

.account_card .add_phone_number:hover, .account_card .add_number:hover, .account_card .blue_text_btn:hover {
  color: #fea500;
}

.account_card .add_number svg, .account_card .add_number img, .account_card .blue_text_btn svg, .account_card .blue_text_btn img {
  width: 16px;
  height: 16px;
  margin-right: 10px;
}

.account_card .add_number svg path, .account_card .add_number img path, .account_card .blue_text_btn svg path, .account_card .blue_text_btn img path {
  fill: #00adef;
}

.account_card .add_number:hover svg path, .account_card .blue_text_btn:hover svg path {
  fill: #fea500;
}

.CircularProgressbar_text h6 {
  font-size: 14px !important;
}

.account-custom-select {
  position: relative;
}

.account-custom-select .header {
  min-height: 56px;
  box-shadow: none;
  color: #fff;
  cursor: pointer;
  background-color: rgba(255, 255, 255, .3);
  border-radius: 1rem;
  outline: none;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: .5rem 1.25rem;
  font-size: 1rem;
  display: flex;
}

.account-custom-select .header span {
  width: 100% !important;
}

.account-custom-select .body {
  z-index: 999;
  background-color: #191c23;
  border: 1px solid rgba(255, 255, 255, .3);
  border-radius: 1rem;
  width: 100%;
  max-height: 250px;
  padding: .5rem 1.25rem;
  position: absolute;
  top: 65px;
  overflow-y: scroll;
}

.account-custom-select .body .search {
  background-color: rgba(255, 255, 255, .3);
  border-radius: 1rem;
  align-items: center;
  gap: 10px;
  width: 100%;
  margin: .5rem 0;
  padding: .5rem 1.25rem;
  display: flex;
}

.account-custom-select .body .search input {
  color: #fff;
  background-color: rgba(0, 0, 0, 0);
  width: 100%;
}

.account-custom-select .body .search input:focus-visible {
  outline: none !important;
}

.account-custom-select .body ul li {
  cursor: pointer;
  border-radius: 8px;
  padding: 10px;
  border-bottom: 0 !important;
  margin-bottom: 0 !important;
}

.account-custom-select .body ul li:hover {
  background-color: rgba(255, 255, 255, .3);
}

.account-custom-select.simple .header {
  background-color: #f2f2f2;
  border: 1px solid rgba(0, 0, 0, .2);
  border-radius: 10px;
  min-height: 34px;
  padding: 7px 10px;
}

.account-custom-select.simple .header span {
  font-size: 14px;
  font-weight: 600;
  color: #000 !important;
}

.account-custom-select.simple .body {
  background-color: #fff;
  border: 1px solid rgba(6, 6, 6, .1);
  border-radius: .94rem;
  width: 396px;
  max-height: 200px;
  padding: 10px 12px;
  top: 45px;
  right: 0;
}

@media only screen and (max-width: 500px) {
  .account-custom-select.simple .body {
    width: auto;
  }
}

.account-custom-select.simple .body.align-left {
  left: 0;
  right: auto;
}

.account-custom-select.simple .body.align-right {
  left: auto;
  right: 0;
}

.account-custom-select.simple .body::-webkit-scrollbar {
  display: block;
}

.account-custom-select.simple .body .option-heading {
  align-items: center;
  gap: 5px;
  margin: 6px 0 2px;
  display: flex;
}

.account-custom-select.simple .body .option-heading p {
  color: #000;
  font-size: 14px;
  font-weight: 700;
}

.account-custom-select.simple .body ul li {
  border-bottom: 1px solid rgba(0, 0, 0, .2);
  padding: 4px 0;
  font-size: 14px;
  font-weight: 600;
  border-radius: 0 !important;
}

.account-custom-select.simple .body ul li:hover {
  background-color: rgba(211, 211, 211, .165);
}

.width-autofit {
  width: -moz-fit-content !important;
  width: fit-content !important;
}

.text_00ADEF {
  color: #00adef !important;
}

input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.switch {
  width: 50px;
  height: 28px;
  display: inline-block;
  position: relative;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  cursor: pointer;
  background-color: #fff;
  border-radius: 34px;
  transition: all .4s;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.slider:before {
  content: "";
  background-color: #9c9a9f;
  border-radius: 50%;
  width: 22px;
  height: 22px;
  transition: all .4s;
  position: absolute;
  bottom: 3px;
  left: 3px;
}

input:checked + .slider {
  background-color: #0099d1;
}

input:checked + .slider:before {
  background-color: #fff;
  transform: translateX(22px);
}

.modal_overlay {
  z-index: 9998;
  background-color: rgba(0, 0, 0, .6);
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
}

.modal_overlay .search_section, .modal_overlay .modal-body {
  z-index: 1000;
  background-color: #031940;
  border: 1px solid #00adef;
  border-radius: 15px;
  width: 1000px;
  max-height: 90vh;
  padding: 20px;
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@media (max-width: 1023px) {
  .modal_overlay .search_section, .modal_overlay .modal-body {
    width: 90%;
  }
}

.modal_overlay .search_section h4, .modal_overlay .modal-body h4 {
  margin-bottom: 20px;
  font-size: 28px;
  font-weight: 400;
}

.modal_overlay .search_section .search_header, .modal_overlay .modal-body .search_header {
  margin-bottom: 20px;
}

.modal_overlay .search_section .search_header .closing_Section, .modal_overlay .modal-body .search_header .closing_Section {
  flex-shrink: 0;
  align-items: center;
  gap: 1rem;
  height: 100%;
  display: flex;
}

@media (max-width: 1023px) {
  .modal_overlay .search_section .search_header .closing_Section, .modal_overlay .modal-body .search_header .closing_Section {
    padding-right: 8px;
  }
}

.modal_overlay .search_section .search_header .closing_Section img, .modal_overlay .modal-body .search_header .closing_Section img {
  cursor: pointer;
}

.modal_overlay .search_section .search_header .search, .modal_overlay .modal-body .search_header .search {
  height: 100%;
}

.modal_overlay .search_section .search_header p, .modal_overlay .modal-body .search_header p {
  white-space: nowrap;
  font-size: 18px;
  font-weight: 600;
}

.modal_overlay .search_section .search_header span, .modal_overlay .modal-body .search_header span {
  white-space: nowrap;
  color: rgba(255, 255, 255, .6);
}

.modal_overlay .search_section .search_header .btn-style, .modal_overlay .modal-body .search_header .btn-style {
  min-width: 100%;
}

.modal_overlay .search_section .search_header button span, .modal_overlay .modal-body .search_header button span {
  color: #fff !important;
}

.modal_overlay .search_section .search, .modal_overlay .modal-body .search {
  background-color: rgba(255, 255, 255, .2);
  border-radius: 15px;
  gap: 10px;
  width: 100%;
  padding: 10px 20px;
  display: flex;
}

.modal_overlay .search_section .search input, .modal_overlay .modal-body .search input {
  color: rgba(255, 255, 255, .6);
  background-color: rgba(0, 0, 0, 0);
  width: 100%;
  height: 100%;
}

.modal_overlay .search_section .search input:focus, .modal_overlay .modal-body .search input:focus {
  box-shadow: none;
  outline: 0;
}

@keyframes fadeOut {
  0% {
    opacity: .9;
  }

  50% {
    opacity: 1;
  }

  90% {
    opacity: .9;
  }

  100% {
    opacity: 0;
  }
}

.modal_overlay .search_section .modal-footer-btn, .modal_overlay .modal-body .modal-footer-btn {
  font-size: 18px;
  min-height: 40px !important;
}

/* [project]/src/css/common/CommonSearch.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.commonSearch {
  align-items: center;
  display: flex;
  position: relative;
}

.commonSearch.searchBtn .form-control {
  color: #000;
  background-color: #fff;
  border: 0;
  border-top-left-radius: 10rem;
  border-bottom-left-radius: 10rem;
  width: calc(100% - 54px);
  min-width: auto;
  min-height: 50px;
  padding: .5rem 1rem;
  font-weight: 600;
}

.commonSearch.searchBtn .form-control::placeholder {
  color: #c5c5d5;
  opacity: 1;
}

.commonSearch.searchBtn .form-control:focus {
  box-shadow: none;
  color: #000;
  background-color: #fff;
  outline: 0;
}

.commonSearch .form-control {
  color: #fff;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  background-color: rgba(255, 255, 255, .3);
  border: 0;
  border-radius: 15px;
  width: 400px;
  min-height: 70px;
  padding: .5rem 1rem .5rem 50px;
  font-size: 1.25rem;
}

@media (max-width: 991px) {
  .commonSearch .form-control {
    min-height: 56px;
    padding-left: 40px;
    font-size: 16px;
  }
}

.commonSearch .form-control:hover {
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.commonSearch .form-control::placeholder {
  color: rgba(255, 255, 255, .8);
  opacity: 1;
}

.commonSearch .form-control:disabled {
  background-color: rgba(0, 0, 0, 0);
}

.commonSearch .form-control:focus {
  box-shadow: none;
  color: #fff;
  background-color: rgba(255, 255, 255, .3);
  border: 0;
  outline: 0;
}

.commonSearch .onlyIcon {
  cursor: pointer;
  position: absolute;
  top: 50%;
  left: 20px;
  transform: translateY(-50%);
}

@media (max-width: 991px) {
  .commonSearch .onlyIcon {
    left: 15px;
  }
}

.commonSearch .btnIcon {
  cursor: pointer;
  background-color: #00adef;
  border-top-right-radius: 10rem;
  border-bottom-right-radius: 10rem;
  justify-content: center;
  align-items: center;
  width: 54px;
  min-height: 50px;
  transition: all .3s ease-in-out;
  display: flex;
}

.commonSearch .btnIcon:hover {
  background-color: #fea500;
}

/* [project]/node_modules/react-circular-progressbar/dist/styles.css [app-client] (css) */
.CircularProgressbar {
  vertical-align: middle;
  width: 100%;
}

.CircularProgressbar .CircularProgressbar-path {
  stroke: #3e98c7;
  stroke-linecap: round;
  transition: stroke-dashoffset .5s;
}

.CircularProgressbar .CircularProgressbar-trail {
  stroke: #d6d6d6;
  stroke-linecap: round;
}

.CircularProgressbar .CircularProgressbar-text {
  fill: #3e98c7;
  dominant-baseline: middle;
  text-anchor: middle;
  font-size: 20px;
}

.CircularProgressbar .CircularProgressbar-background {
  fill: #d6d6d6;
}

.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-background {
  fill: #3e98c7;
}

.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-text {
  fill: #fff;
}

.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-path {
  stroke: #fff;
}

.CircularProgressbar.CircularProgressbar-inverted .CircularProgressbar-trail {
  stroke: rgba(0, 0, 0, 0);
}

/* [project]/src/css/common/CommonTable.scss.css [app-client] (css) */
:root {
  --font-gilroy: "Gilroy", sans-serif;
}

.tableless {
  z-index: 1;
  border: 1px solid #00adef;
  border-radius: 2rem;
  position: relative;
}

.tableless:before {
  content: "";
  z-index: -1;
  background-image: url("https://cdn.tradereply.com/dev/site-assets/tradereply-crypto-stock-analysis.png");
  background-position: center;
  background-repeat: no-repeat;
  background-size: 100%;
  border-radius: 2rem;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.tableless .common_table tr th, .tableless .common_table tr td {
  white-space: nowrap;
  vertical-align: middle;
  border: 1px solid #34415b;
  padding: 1.6rem 1rem;
}

@media (max-width: 767px) {
  .tableless .common_table tr th, .tableless .common_table tr td {
    padding: 1.5rem .5rem;
  }
}

.tableless .common_table tr th .clickIcon, .tableless .common_table tr td .clickIcon {
  cursor: pointer;
}

.tableless .common_table tr td {
  font-weight: 500 !important;
}

.tableless .common_table thead {
  border: none;
}

.tableless .common_table thead tr {
  position: -webkit-sticky;
  position: sticky;
  top: 0;
}

.tableless .common_table thead tr th {
  color: #fff;
  background-color: #000;
  border-top: 0;
  border-radius: 0;
  font-size: 1.25rem;
  font-weight: 600;
}

@media (max-width: 1199px) {
  .tableless .common_table thead tr th {
    font-size: 1rem;
  }
}

@media (max-width: 767px) {
  .tableless .common_table thead tr th {
    font-size: .875rem;
  }
}

.tableless .common_table thead tr th:first-child {
  border-left: 0;
  border-top-left-radius: 2rem;
}

.tableless .common_table thead tr th:last-child {
  border-right: 0;
  border-top-right-radius: 2rem;
}

.tableless .common_table tbody tr:last-child, .tableless .common_table tbody tr:last-child td {
  border-bottom: 0;
}

.tableless .common_table tbody td {
  color: #e9e9e9;
  background: none;
  font-size: 1.125rem;
  font-weight: 600;
}

@media (max-width: 1199px) {
  .tableless .common_table tbody td {
    font-size: 1rem;
  }
}

@media (max-width: 767px) {
  .tableless .common_table tbody td {
    font-size: .875rem;
  }
}

.tableless .common_table tbody td a {
  color: #00adef;
}

.tableless .common_table tbody td:first-child {
  border-left: 0;
}

.tableless .common_table tbody td:last-child {
  border-right: 0;
}

.tableless .common_table tbody tr.no_record td {
  padding: 1rem 0;
}

.no_record_box {
  text-align: center;
  padding: 3.125rem 1rem;
}

.no_record_box svg {
  opacity: 1;
}

.no_record_box svg path {
  fill: #fff;
}

.no_record_box img {
  width: 150px !important;
}

.no_record_box h4 {
  color: #fff;
  margin-top: .625rem;
  font-size: 1rem;
  font-weight: 600;
}

@media (max-width: 1679px) {
  .no_record_box h4 {
    font-size: .875rem;
  }
}

@media (max-width: 991px) {
  .no_record_box {
    padding: 2.5rem 1.25rem;
  }
}

/*# sourceMappingURL=_3a27fccd._.css.map*/