import Cookies from 'js-cookie';

/**
 * Remember 2FA Cookie Utilities
 * 
 * Provides functions for managing the remember_2fa cookie that allows
 * users to bypass 2FA verification for 30 days after successful verification.
 */

/**
 * Get the remember 2FA cookie name from configuration
 */
export const getRemember2FACookieName = () => {
    return 'remember_2fa';
};

/**
 * Check if remember 2FA cookie exists
 */
export const hasRemember2FACookie = () => {
    if (typeof window === 'undefined') {
        return false;
    }
    const cookieName = getRemember2FACookieName();
    const cookieValue = Cookies.get(cookieName);
    return !!cookieValue;
};

/**
 * Get the remember 2FA cookie value
 */
export const getRemember2FACookie = () => {
    if (typeof window === 'undefined') {
        return null;
    }
    const cookieName = getRemember2FACookieName();
    return Cookies.get(cookieName) || null;
};

/**
 * Validate remember 2FA cookie structure (client-side basic validation)
 * Note: Full cryptographic validation happens on the backend
 */
export const isRemember2FACookieValid = () => {
    try {
        const cookieValue = getRemember2FACookie();

        if (!cookieValue) {
            return false;
        }

        // Check if we're in browser environment
        if (typeof window === 'undefined') {
            return false;
        }

        // Basic structure validation - check if it's base64 encoded JSON
        const decoded = atob(cookieValue);
        const cookieData = JSON.parse(decoded);
        
        // Check if it has the expected structure
        if (!cookieData || !cookieData.data || !cookieData.signature) {
            return false;
        }

        // Parse the inner data
        const innerData = JSON.parse(cookieData.data);
        
        // Check if it has required fields
        if (!innerData || !innerData.user_id || !innerData.timestamp || !innerData.type) {
            return false;
        }

        // Check if it's the right type
        if (innerData.type !== 'remember_2fa') {
            return false;
        }

        // Basic expiration check (30 days)
        const timestamp = new Date(innerData.timestamp);
        const now = new Date();
        const daysDiff = (now - timestamp) / (1000 * 60 * 60 * 24);
        
        if (daysDiff > 30) {
            console.log('Remember 2FA cookie expired (client-side check)');
            return false;
        }

        return true;
    } catch (error) {
        console.warn('Error validating remember 2FA cookie:', error);
        return false;
    }
};

/**
 * Remove the remember 2FA cookie
 */
export const removeRemember2FACookie = () => {
    if (typeof window === 'undefined') {
        return;
    }
    const cookieName = getRemember2FACookieName();
    Cookies.remove(cookieName);
    Cookies.remove(cookieName, { path: '/' }); // Also try with explicit path
};

/**
 * Get remember 2FA cookie validation status for login API
 * Returns an object with validation status and cookie value
 */
export const getRemember2FAValidationStatus = () => {
    const cookieValue = getRemember2FACookie();



    if (!cookieValue) {
        return {
            status: 'missing',
            cookieValue: null,
            isValid: false
        };
    }

    const isValid = isRemember2FACookieValid();



    return {
        status: isValid ? 'valid' : 'invalid',
        cookieValue: cookieValue,
        isValid: isValid
    };
};



/**
 * Monitor remember 2FA cookie and clean up if expired
 * This can be called periodically to clean up expired cookies
 */
export const cleanupExpiredRemember2FACookie = () => {
    if (hasRemember2FACookie() && !isRemember2FACookieValid()) {
        console.log('Removing expired remember 2FA cookie');
        removeRemember2FACookie();
        return true; // Cookie was removed
    }
    return false; // No action taken
};
